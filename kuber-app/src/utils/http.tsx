import axios from 'axios';
import config from './config';
import AsyncStorage from '@react-native-async-storage/async-storage';

const instance = axios.create({
    baseURL: config.BASE_URL,
    timeout: 30000,
    headers: {}
});

instance.interceptors.request.use(
    async (config: any) => {
        config.headers.authorization = `Bearer ${await AsyncStorage.getItem(
            'AuthToken'
        )}`;
        return config;
    },
    (error) => errorHandler(error)
);

instance.interceptors.response.use(
    (response) => {
        const responseData = JSON.stringify(responseHandler(response.data));
        Promise.resolve(responseData);
        return responseData;
    },
    async (error) => {
        return Promise.reject(errorHandler(error));
    }
);

const errorHandler = (error: string) => {
    return error;
};

const responseHandler = (response: any) => {
    return response;
};

export function httpGet(url: string) {
    return instance.get(url);
}

export function httpPost(url: string, payload: any) {
    return instance.post(url, payload);
}

export function httpPut(url: string, payload: any) {
    return instance.put(url, payload);
}

export function httpDelete(url: string) {
    return instance.delete(url);
}

export function httpPatch(url: string, data: any) {
  return instance.patch(url, data);
};
