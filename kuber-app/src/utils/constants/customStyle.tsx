import React from 'react';
import { Text, TextStyle } from 'react-native';

const CustomTextStyle = () => {
  const headingText = 22;
  const headingsText = 24;
  const smallHeadingText = 20;
  const lageHeadingText = 30;
  const extraLarge = 18;
  const large = 16;
  const medium = 14;
  const small = 12;
  const extraSmall = 10;

  const normalText = (textColor:String, size:Number) => ({
    color: textColor,
    fontSize: size,
    fontFamily: 'MontserratRegular'
  });
  
  const boldText = (textColor:String, size:Number) => ({
    color: textColor,
    fontWeight: 'bold',
    fontSize: size,
    fontFamily: 'MontserratBold',
  });

  const boldItalicText = (textColor:String, size:Number) => ({
    color: textColor,
    fontSize: size,
    fontFamily: 'CormorantBoldItalic',
  });

  const semiBoldText = (textColor:String, size:Number) => ({
    color: textColor,
    fontSize: size,
    fontFamily: 'MontserratSemiBold',
  });

  const medernText = (textColor:String, size:Number) => ({
    color: textColor,
    fontSize: size,
    fontFamily: 'KINFOLKModern',
  });

  return null; 
};

export default CustomTextStyle;
