import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { httpGet } from '../../utils/http';
import AttendanceHistoryView from './AttendanceHistoryView';
import { View, BackHandler, FlatList } from 'react-native';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { color } from '../../utils/constants/color';
import styles from './styles';
import { stringText } from '../../utils/constants/stringsText';
import { AttendanceHistoryResponse } from '../../interfaces';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import RNButton from '../../component/RNButton';

export type Props = {
    navigation: any;
};

const AttendanceHistoryScreen = (props: Props) => {
    const { navigation } = props;
    const [datesearch, setDateSearch] = useState(new Date());
    const [datelast, setDateLast] = useState(new Date());
    const [opensearch, setOpenSearch] = useState<boolean>(false);
    const [openlast, setOpenlast] = useState<boolean>(false);
    const [fetching, setFetching] = useState<boolean>(false);
    const [tableData, setTableData] = useState<
        Array<AttendanceHistoryResponse> | any
    >();
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);

    useEffect(() => {
        LoadHistory();
    }, [datesearch, datelast]);
    useEffect(() => {
        const backAction = () => {
            navigation.navigate(navigationStringText.DailyAttendance);
            return true; // Prevent default behavior (exit the app)
        };

        const backHandler = BackHandler.addEventListener(
            'hardwareBackPress',
            backAction
        );

        return () => backHandler.remove();
    }, [navigation]);
    const LoadHistory = () => {
        if (datesearch?.valueOf() > datelast?.valueOf()) {
            errorHandlerClicked(true, `${stringText.SelectCorrectDate}`);
        } else {
            setFetching(true);
            const url = `/attendance_history?order_type=2&from_date=${moment(
                datesearch
            ).format('YYYY-MM-DD')}&to_date=${moment(datelast).format(
                'YYYY-MM-DD'
            )}`;
            httpGet(url)
                .then((response: any) => {
                    const data = JSON.parse(response)?.data;
                    if (data) {
                        const tableColumn: Array<any> = data.map(
                            (item: any) => {
                                const date = item?.date;
                                const inTime = item?.in_time;
                                const outTime = item?.out_time;
                                const totalHrs = item?.total_hours;
                                const innerItem = [
                                    date,
                                    inTime,
                                    outTime,
                                    totalHrs,
                                    getLogs(item?.id)
                                ];
                                return innerItem;
                            }
                        );
                        const tableData: any = {
                            tableHead: [
                                `${stringText.DateText}`,
                                `${stringText.InTime}`,
                                `${stringText.OutTime}`,
                                `${stringText.TotalHrs}`,
                                `${stringText.ViewLogs}`
                            ],
                            tableData: tableColumn
                        };
                        setTableData(tableData);
                    }
                    setFetching(false);
                })
                .catch((err) => {
                    setFetching(false);
                    errorHandlerClicked(
                        true,
                        err?.response?.data?.message ||
                            stringText.SomethingWentwrong
                    );
                });
        }
    };

    const getLogs = (userId: any) => {
        return (
            <View style={styles.viewLogsView}>
                <RNButton
                    handleOnPress={() =>
                        navigation.navigate(
                            navigationStringText.AttendanceLogs,
                            { userId: userId }
                        )
                    }
                >
                    <EvilIcons
                        name="exclamation"
                        color={color.DARK_BLUE}
                        size={24}
                    />
                </RNButton>
            </View>
        );
    };

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    return (
        <AttendanceHistoryView
            navigation={navigation}
            datesearch={datesearch}
            setOpenSearch={setOpenSearch}
            datelast={datelast}
            setOpenlast={setOpenlast}
            opensearch={opensearch}
            setDateSearch={setDateSearch}
            openlast={openlast}
            setDateLast={setDateLast}
            tableData={tableData}
            fetching={fetching}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
        />
    );
};

export default AttendanceHistoryScreen;
