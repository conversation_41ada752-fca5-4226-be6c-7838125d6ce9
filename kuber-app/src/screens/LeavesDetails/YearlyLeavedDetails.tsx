import React from "react";
import { View } from "react-native";
import styles from "./styles";
import { color } from "../../utils/constants/color";
import RNText from "../../component/RNText";
import Ionicons from 'react-native-vector-icons/Ionicons';
import LeavedDetailsTable from "./LeavedDetailsTable";
import { stringText } from "../../utils/constants/stringsText";
import RNButton from "../../component/RNButton";

const YearlyLeavedDetails: React.FC<{
    leaveAccrualListData: {
        totalLeaveCount: number;
        financialYear: string;
        deductibleLeavesAvailed: [];
        nonDeductibleLeavesAvailed: [];
        allocatedLeaves: [];
        totalDeductibleLeaves: number;
        totalNonDeductibleLeavesAvailed: number;
    };
    handelToggal: (year:string) => void;
    isVisible: string;
}> = ({ leaveAccrualListData, handelToggal, isVisible }) => {
    return (
        <View style={{ marginVertical: 10,paddingHorizontal:10 }}>
            <RNButton
                ActiveOpacity={0.8}
                handleOnPress={() => handelToggal(leaveAccrualListData.financialYear.toString())}
                style={[
                    styles.tableTopTitleTextWrapper,
                    {
                        backgroundColor: isVisible == leaveAccrualListData.financialYear
                            ? color.WHITE
                            : color.WHITE,
                        borderRadius: isVisible == leaveAccrualListData.financialYear ? 0 : 6,
                        borderTopLeftRadius: 6,
                        borderTopRightRadius:6,
                        elevation: 2,
                    }
                ]}
            >
                <RNText style={styles.tableTopTitleText}>
                    {stringText.LeavesDetailsfortheFinancialYear}{' '}
                    {leaveAccrualListData.financialYear}
                </RNText>
                <Ionicons
                    name={isVisible == leaveAccrualListData.financialYear ? 'chevron-up' : 'chevron-down'}
                    size={20}
                    color={color.BLACK}
                />
            </RNButton>
            {isVisible == leaveAccrualListData.financialYear && (
                <LeavedDetailsTable
                    LeavedDetails={leaveAccrualListData.allocatedLeaves}
                    title="Allocated Leaves"
                    total={leaveAccrualListData.totalLeaveCount}
                    totalTitle="Total Leaves Allocated:"
                />
            )}
            {isVisible == leaveAccrualListData.financialYear && (
                <LeavedDetailsTable
                    LeavedDetails={
                        leaveAccrualListData.deductibleLeavesAvailed
                    }
                    title="Deductible Leaves Availed"
                    total={leaveAccrualListData.totalDeductibleLeaves}
                    totalTitle="Total Deductible Leaves:"
                />
            )}
            {isVisible == leaveAccrualListData.financialYear && (
                <LeavedDetailsTable
                    LeavedDetails={
                        leaveAccrualListData.nonDeductibleLeavesAvailed
                    }
                    title="Non Deductible Leaves Availed"
                    total={leaveAccrualListData.totalNonDeductibleLeavesAvailed}
                    totalTitle="Total Non Deductible Leaves:"
                />
            )}
        </View>
    );
};

export default YearlyLeavedDetails;