import React, { useEffect, useState } from 'react';
import ServiceRequestsView from './LeavesDetailsView';
import LeavesDetailsView from './LeavesDetailsView';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import { stringText } from '../../utils/constants/stringsText';
import { httpGet } from '../../utils/http';
import apiConstant from '../../utils/constants/apiConstant';

export type Props = {
    route: any;
    navigation: any;
};

export type userLeavesDetailsType = {
    employeeId: string;
    employeeName: string;
    designation: string;
    accrualType: string;
    totalLeaves: number;
    prevYearTotalLeaves: number;
    availedLeaves: number;
    prevAvailedLeaves: number;
    currentYearLeaves: number;
    previousYearLeaves: number;
    futureYearTotalLeaves: number;
    encashedLeaves: number;
    balanceLeaves: number;
    leaveAccrualList: {
        totalLeaveCount: number;
        financialYear: string;
        deductibleLeavesAvailed: {
            leaveType: string;
            leaveStartDate: string;
            leaveEndDate: string;
            count: number;
            desc: string;
            totalDeductibleLeaves: number;
        }[];
        nonDeductibleLeavesAvailed: {
            leaveType: string;
            leaveStartDate: string;
            leaveEndDate: string;
            count: number;
            desc: string;
            totalDeductibleLeaves: number;
        }[];
        allocatedLeaves: any[]; 
        totalDeductibleLeaves: number;
        totalNonDeductibleLeavesAvailed: number;
    }[]; 
};

const LeavesDetailsScreen = (props: Props) => {
    const { route, navigation } = props;    
    
    const GoToEditServiceRequests = () => {
        navigation.navigate(navigationStringText.EditServiceRequests);
    };

    const [fetching, setFetching] = useState<boolean>(false);
    const [userLeavesDetails, setUserLeavesDetails] = useState<userLeavesDetailsType>(
        {
            employeeId: '',
            employeeName: '',
            designation: '',
            accrualType: '',
            totalLeaves: 0,
            prevYearTotalLeaves: 0,
            availedLeaves: 0,
            prevAvailedLeaves: 0,
            currentYearLeaves: 0,
            previousYearLeaves: 0,
            futureYearTotalLeaves: 0,
            encashedLeaves: 0,
            balanceLeaves: 0,
            leaveAccrualList: [],
        }
    )
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [drsData, setDrsData] = useState<any>();



    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setFetching(true);
        httpGet(apiConstant.DRs_LIST)
            .then((response: any) => {
                const drsData =
                    JSON.parse(response)?.data
                setDrsData(drsData)
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.responsesetDrsData?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }, []);

    useEffect(() => {
        setFetching(true);
            if(drsData) {
                httpGet(`${apiConstant.LEAVES}?userId=${props?.route?.params?.userId ? props?.route?.params?.userId : drsData?.userDetails.userId}`
            )
                .then((response: any) => {
                    const data =
                        JSON.parse(response)?.data                        
                        setUserLeavesDetails(data);
                    setFetching(false);

                })
                .catch((err) => {
                    setFetching(false);
                    if (
                        err?.response?.data?.message !== undefined &&
                        err?.response?.data?.message !== null &&
                        err?.responsesetDrsData?.data?.message !== ''
                    ) {
                        errorHandlerClicked(true, err?.response?.data?.message);
                    } else {
                        errorHandlerClicked(
                            true,
                            `${stringText.SomethingWentwrong}`
                        );
                    }
                });
            };
    }, [drsData]);

    return (
        <LeavesDetailsView
            GoToEditServiceRequests={GoToEditServiceRequests}
            userLeavesDetails={userLeavesDetails}
            fetching={fetching}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
        />
    );
};

export default LeavesDetailsScreen;
