import React, { useEffect, useState } from 'react';
import EditServiceRequestsView from './EditServiceRequestsView';
import { httpGet, httpPost } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import { Alert, ToastAndroid } from 'react-native';
import moment from 'moment';
import config from '../../utils/config';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { successToast } from '../../component/SuccessToast';
import { SRFieldName } from './FieldNames';
import apiConstant from '../../utils/constants/apiConstant';

export type Props = {
    navigation: any;
    route: any;
};

export type departmentType = {
    id: string | number;
    dept_name: string;
    dept_code: string;
};
export type departmentDropDownType = { label: string; value: departmentType };

export type requestType = { id: number | string; title: string };
export type requestDropDownType = {
    label: string;
    value: requestType;
};

export type priorityType = {
    label: string;
    value: any;
};

export type currentStatusType = {
    label: string;
    value: any;
};

export type ServiceDataTypes = {
    id: number | null;
    id_department: number | null;
    title: string;
    description: string;
    priority: number;
    status: number;
    project_group: string | null;
    id_issue_type: number;
    amount: number | string | null;
    file: any;
    file_paths: string[] | string | null;
    created_at: string; // Date string in ISO 8601 format
    created_by: number;
    srId: string;
    dueBy: string;
    issues: {
        id: number;
        title: string;
    };
    department: {
        id: number;
        dept_code: string;
        dept_name: string;
    };
    employeeId: string;
    employeeName: string;
    comments: any[];
    leaveDates: {
        id_employee: number;
        leave_start_date: string;
        leave_end_date: string; // Date string in ISO 8601 format
    };
    leaveFor: number;
    userId: number;
};

const priorityData = [
    { label: 'Low', value: '0' },
    { label: 'Normal', value: '1' },
    { label: 'High', value: '2' },
    { label: 'Urgent', value: '3' },
    { label: 'Immediate', value: '4' }
];

const statusData = [
    { label: 'Open', value: 1 },
    { label: 'In progress', value: 2 },
    { label: "Won't fix", value: 4 },
    { label: 'Closed', value: 5 },
    { label: 'Re-open', value: 6 }
];

const EditServiceRequests = (props: Props) => {
    const { navigation, route } = props;

    const { srId } = route.params;

    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [serviceData, setServiceData] = useState<ServiceDataTypes>({
        id: null,
        id_department: null,
        title: '',
        description: '',
        priority: 0,
        status: 1,
        project_group: '',
        id_issue_type: 0,
        amount: null,
        file: null,
        file_paths: null,
        created_at: '',
        created_by: 0,
        srId: '',
        dueBy: '',
        issues: {
            id: 0,
            title: ''
        },
        department: {
            id: 0,
            dept_code: '',
            dept_name: ''
        },
        employeeId: '',
        employeeName: '',
        comments: [],
        leaveDates: {
            id_employee: 0,
            leave_start_date: '',
            leave_end_date: ''
        },
        leaveFor: 0,
        userId: 0
    });
    const [startDate, setStartDate] = useState<Date>(new Date());
    const [endDate, setEndDate] = useState<Date>(new Date());

    const [serviceDataInput, setServiceDataInput] = useState<ServiceDataTypes>({
        id: null,
        id_department: null,
        title: '',
        description: '',
        priority: 0,
        status: 1,
        project_group: '',
        id_issue_type: 0,
        amount: null,
        file: null,
        file_paths: null,
        created_at: '',
        created_by: 0,
        srId: '',
        dueBy: '',
        issues: {
            id: 0,
            title: ''
        },
        department: {
            id: 0,
            dept_code: '',
            dept_name: ''
        },
        employeeId: '',
        employeeName: '',
        comments: [],
        leaveDates: {
            id_employee: 0,
            leave_start_date: startDate.toISOString(),
            leave_end_date: endDate.toISOString()
        },
        leaveFor: 1,
        userId: 0
    });
    const [departmentData, setDepartmentData] = useState<departmentType[]>([]);
    const [requestTypeData, setRequestTypeData] = useState<requestType[]>([]);

    const [department, setDepartment] = useState<departmentDropDownType>({
        label: serviceData?.department?.dept_name,
        value: serviceData?.department
    });
    const [requestType, setRequestType] = useState<requestDropDownType>({
        label: serviceData?.issues?.title,
        value: { id: -1, title: '' }
    });

    const [priority, setPriority] = useState<priorityType>({
        label: '',
        value: serviceData?.priority
    });

    const [currentStatus, setCurrentStatus] = useState<currentStatusType>({
        label: 'Open',
        value: 1
    });

    const buttons = ['Self', 'DRs'];
    const [selectedIndex, setSelectedIndex] = useState(0);

    const [startDateOpen, setStartDateOpen] = useState<boolean>(false);
    const [endDateOpen, setEndDateOpen] = useState<boolean>(false);

    const [isAddCommentOpen, setIsAddCommentOpen] = useState<boolean>(false);
    const [comment, setComment] = useState('');
    const [errors, setErrors] = useState<any>({});
    const [selectedFile, setSelectedFile] = useState<any>(null);

    const DeparmentDropDownData = departmentData.map(
        (item: departmentType) => ({
            label: item.dept_name,
            value: item
        })
    );

    const requestTypeDataFromDB = requestTypeData.map((item: requestType) => ({
        label: item.title?.replace(/\n/g, ''),
        value: item
    }));

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setDepartment({
            label: serviceData?.department?.dept_name,
            value: serviceData?.department
        });

        setRequestType({
            label: serviceData?.issues?.title,
            value: serviceData?.issues
        });
    }, [serviceData?.id, serviceData?.issues]);

    useEffect(() => {
        setFetching(true);
        if (srId) {
            getSrDataById(srId);
            httpGet(apiConstant.DEPARTMENT)
                .then((response: any) => {
                    const departments = JSON.parse(response)?.data;
                    setDepartmentData(departments);
                    setServiceDataInput({
                        ...serviceDataInput,
                        department: departments[0]
                    });
                    setFetching(false);
                })
                .catch((err) => {
                    setFetching(false);
                    if (
                        err?.response?.data?.message !== undefined &&
                        err?.response?.data?.message !== null &&
                        err?.response?.data?.message !== ''
                    ) {
                        errorHandlerClicked(true, err?.response?.data?.message);
                    } else {
                        errorHandlerClicked(
                            true,
                            `${stringText.SomethingWentwrong}`
                        );
                    }
                });
        } else {
            httpGet(apiConstant.DEPARTMENT)
                .then((response: any) => {
                    const departments = JSON.parse(response)?.data;
                    setDepartmentData(departments);
                    setServiceDataInput({
                        ...serviceDataInput,
                        department: departments[0]
                    });
                    getDepartmentById(departments[0].id);
                    setFetching(false);
                })
                .catch((err) => {
                    setFetching(false);
                    if (
                        err?.response?.data?.message !== undefined &&
                        err?.response?.data?.message !== null &&
                        err?.response?.data?.message !== ''
                    ) {
                        errorHandlerClicked(true, err?.response?.data?.message);
                    } else {
                        errorHandlerClicked(
                            true,
                            `${stringText.SomethingWentwrong}`
                        );
                    }
                });
        }
    }, []);

    const getDepartmentById = (departmentId: string | number) => {
        setFetching(true);
        httpGet(`${apiConstant.REQUEST_TYPE}?dept_id=${departmentId}`)
            .then((response: any) => {
                const requestTypeByID = JSON.parse(response)?.data;
                setRequestTypeData(requestTypeByID);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    useEffect(() => {
        setServiceDataInput({
            ...serviceDataInput,
            leaveDates: {
                ...serviceDataInput.leaveDates,
                leave_start_date: startDate.toISOString(),
                leave_end_date: endDate.toISOString()
            }
        });
    }, [startDate, endDate]);

    const handelUpdateServiceData = (
        name: string,
        value: string | number | object
    ) => {
        setServiceData({ ...serviceData, [name]: value });
    };

    const timeAgo = (date: string) => {
        let momentOne = moment(new Date(date))
        let momentTwo = moment(new Date())
        let result = moment.duration(momentOne.diff(momentTwo))
        return result.humanize()
      } 
    const formValidation = (type: any, data?: any) => {   
        let isValid = true;
        const newErrors: any = errors;
        switch (type) {
            case SRFieldName.Department:
                if (!data) {
                    newErrors[SRFieldName.Department] = `Please select Department`;
                    isValid = false;
                } else {
                    newErrors[SRFieldName.Department] = '';
                }
                setErrors(newErrors);
                break;
            case SRFieldName.Issue:
                if (!data) {
                    newErrors[SRFieldName.Issue] = `Please select Request Type`;
                    isValid = false;
                } else {
                    newErrors[SRFieldName.Issue] = '';
                }
                setErrors(newErrors);
                break;

            case SRFieldName.Priority:
                if (!data) {
                    newErrors[SRFieldName.Priority] = `Please select Priority`;
                    isValid = false;
                }
                else {
                    newErrors[SRFieldName.Priority] = '';
                }
                setErrors(newErrors);
                break;

            case SRFieldName.Title:

                if (!data) {
                    newErrors[SRFieldName.Title] = `Please enter the title`;
                    isValid = false;

                } else {
                    const regex = /^[a-zA-Z0-9\s!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]+$/;
                    const isValidFormat = regex.test(data);
                    const startsWithSpace = /^\s/.test(data);
                    if (!isValidFormat) {
                        newErrors[SRFieldName.Title] = 'Title must contain only strings and numbers.';
                        isValid = false;
                    } else if (startsWithSpace) {
                        newErrors[SRFieldName.Title] = 'Title should not start with empty spaces.';
                        isValid = false;

                    } else {
                        newErrors[SRFieldName.Title] = '';
                    }
                }
                setErrors(newErrors);
                break;

            case SRFieldName.Description:
                if (!data) {
                    newErrors[SRFieldName.Description] = `Please enter the description`;
                    isValid = false;
                } else {
                    const regex = /^[a-zA-Z0-9\s@.]+$/;
                    const isValidFormat = regex.test(data);
                    const startsWithSpace = /^\s/.test(data);

                    if (startsWithSpace) {
                        newErrors[SRFieldName.Description] =
                            'Description should not start with empty spaces.';
                        isValid = false;
                    } else {
                        newErrors[SRFieldName.Description] = '';
                    }
                }
                setErrors(newErrors);
                break;

            case SRFieldName.StartLeaveDates:
            case SRFieldName.EndLeaveDates:
                const startDateCase = new Date(type==SRFieldName.StartLeaveDates ? data : startDate);
                const endDateCase = new Date(type==SRFieldName.EndLeaveDates ? data : endDate);
                const today = new Date();
                if (
                    startDateCase.toISOString() < today.toISOString() &&
                    startDateCase.toDateString() !== today.toDateString()
                ) {
                    newErrors[SRFieldName.StartLeaveDates] = 'Start date must be a future date';
                    isValid = false;
                }else if (endDateCase < startDateCase) {
                    newErrors[SRFieldName.StartLeaveDates] = 'End date cannot be smaller than the start date';
                    isValid = false;
                }else{
                    newErrors[SRFieldName.StartLeaveDates] = '';
                }
                setErrors(newErrors);
                break;

            case SRFieldName.Project_group:
                if (
                    requestType?.value?.id == '80' &&
                    department?.value?.id == '3'
                ) {
                    if (!data) {
                        newErrors[SRFieldName.Project_group] = `Please enter the Project Group`;
                        isValid = false;
                    } else {
                        const regex = /^\s*[^,\s@]+@[^\s@]+\.[^\s@]+(?:\s*,\s*[^,\s@]+@[^\s@]+\.[^\s@]+)*\s*$/;
                        const isValidFormat = regex.test(data);
                        const startsWithSpace = /^\s/.test(data);
                        if (!isValidFormat) {
                            newErrors[SRFieldName.Project_group] = 'Please enter valid email address.';
                            isValid = false;
                        } else if (startsWithSpace) {
                            newErrors[SRFieldName.Project_group] = 'Project group should not start with empty spaces.';
                            isValid = false;
                        } else {
                            newErrors[SRFieldName.Project_group] = '';
                        }
                    }
                }
                setErrors(newErrors);
                break;

            case SRFieldName.File:
                if (
                    (requestType?.value?.id == '34' && department?.value.id == '4') ||
                    (requestType?.value?.id == '89' && department?.value.id == '11')
                ) {
                    if (!data) {
                        newErrors[SRFieldName.File] = `Please select the File`;
                        isValid = false;
                    } else {
                        newErrors[SRFieldName.File] = '';
                    }
                }
                setErrors(newErrors);
                break;

            case SRFieldName.Amount:
                if (requestType?.value?.id == 34 && department?.value.id == 4) {
                    const startsWithSpace = /^\s/.test(data);
                    if (startsWithSpace) {
                        newErrors[SRFieldName.Amount] = 'Amount should not start with empty spaces.';
                        isValid = false;
                    } else {
                        newErrors[SRFieldName.Amount] = '';
                    }
                } else {
                    newErrors[SRFieldName.Amount] = '';
                }
                setErrors(newErrors);
                break;
            default:
                break;
        }
        return isValid;
    }
    const isFormValid = (data: any) => {
        const requiredFields = [
            { field: 'department', label: 'Department' },
            { field: 'issues', label: 'Issue' },
            { field: 'priority', label: 'Priority' },
            { field: 'title', label: 'Title' },
            { field: 'description', label: 'Description' },
            { field: 'leaveDates', label: 'leaveDates' }
        ];
        if (requestType?.value?.id == '80' && department?.value?.id == '3') {
            requiredFields.push({
                field: 'project_group',
                label: 'Project Group'
            });
        } else {
            requiredFields.filter((item) => item.field !== 'project_group');
        }
        if (
            (requestType?.value?.id == '34' && department?.value.id == '4') ||
            (requestType?.value?.id == '89' && department?.value.id == '11')
        ) {
            requiredFields.push({
                field: 'file',
                label: 'File'
            });
        } else {
            requiredFields.filter((item) => item.field !== 'file');
        }
        if (requestType?.value?.id == 34 && department?.value.id == 4) {
            requiredFields.push({ field: 'amount', label: 'Amount' });
        } else {
            requiredFields.filter((item) => item.field !== 'amount');
        }
        let isValid = true;
        const newErrors: any = {};

        for (const { field, label } of requiredFields) {
            if (!data[field]) {
                if (
                    (requestType?.value?.id == '34' &&
                        department?.value.id == '4') ||
                    (requestType?.value?.id == '89' &&
                        department?.value.id == '11')
                ) {
                    newErrors[field] = `Please ${field === 'title' || field === 'description' || field === 'amount' ? 'enter' : 'select'} the ${label}`;
                    isValid = false;
                } else if (field === 'title' || field === 'description' || field === 'project_group') {
                    newErrors[field] = `Please enter the ${label}`;
                    isValid = false;
                } else {
                    newErrors[field] = `Please select the ${label}`;
                    isValid = false;
                }
            }
            if (field === 'issues' && !data[field].id) {
                newErrors[field] = `Please select Request Type`;
                isValid = false;
            }
            if (field === 'title' && data[field]) {
                const regex =
                    /^[a-zA-Z0-9\s!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]+$/;
                const isValidFormat = regex.test(data[field]);
                const startsWithSpace = /^\s/.test(data[field]);

                if (!isValidFormat) {
                    newErrors[field] =
                        'Title must contain only strings and numbers.';
                    isValid = false;
                } else if (startsWithSpace) {
                    newErrors[field] =
                        'Title should not start with empty spaces.';
                    isValid = false;
                }
            }

            if (field === 'description' && data[field]) {
                const regex = /^[a-zA-Z0-9\s@.]+$/;
                const isValidFormat = regex.test(data[field]);
                const startsWithSpace = /^\s/.test(data[field]);

                if (startsWithSpace) {
                    newErrors[field] =
                        'Description should not start with empty spaces.';
                    isValid = false;
                }
            }


            if (field === 'leaveDates') {
                const { leave_start_date, leave_end_date } = data[field];
                const startDate = new Date(leave_start_date);
                const endDate = new Date(leave_end_date);
                const today = new Date();

                if (
                    startDate.toISOString() < today.toISOString() &&
                    startDate.toDateString() !== today.toDateString()
                ) {
                    newErrors[field] = 'Start date must be a future date';
                    isValid = false;
                }
                if (endDate < startDate) {
                    newErrors[field] =
                        'End date cannot be smaller than the start date';
                    isValid = false;
                }
            }
            if (
                requestType?.value?.id == '80' &&
                department?.value?.id == '3'
            ) {
                if (field === 'project_group' && data[field]) {
                    const regex =
                        /^[^\s@]+@[^\s@]+\.[^\s@]+(?:\s*,\s*[^\s@]+@[^\s@]+\.[^\s@]+)*$/g;
                    const isValidFormat = regex.test(data[field]);
                    const startsWithSpace = /^\s/.test(data[field]);
                    if (!isValidFormat) {
                        newErrors[field] = 'Please enter valid email address.';
                        isValid = false;
                    } else if (startsWithSpace) {
                        newErrors[field] =
                            'Project group should not start with empty spaces.';
                        isValid = false;
                    }
                }
            }
            if (
                (requestType?.value?.id == '34' &&
                    department?.value.id == '4') ||
                (requestType?.value?.id == '89' && department?.value.id == '11')
            ) {
                if (field === 'file' && data[field]) {
                    //no more validation in upload file field
                }
            }
            if (requestType?.value?.id == 34 && department?.value.id == 4) {
                if (field === 'amount' && data[field]) {
                    const startsWithSpace = /^\s/.test(data[field]);
                    if (startsWithSpace) {
                        newErrors[field] =
                            'Amount should not start with empty spaces.';
                        isValid = false;
                    }
                }
            }
        }

        setErrors(newErrors);

        return isValid;
    };

    const handelSave = async () => {
        setFetching(true);
        ({
            id_department: serviceDataInput.department.id,
            priority: serviceDataInput.priority,
            status: serviceDataInput.status,
            title: serviceDataInput.title,
            id_issue_type: serviceDataInput.issues.id,
            leave_for: serviceDataInput.leaveFor,
            employeeId: serviceDataInput.employeeId,
            description: serviceDataInput.description,
            leave_start_date: serviceDataInput.leaveDates.leave_start_date,
            leave_end_date: serviceDataInput.leaveDates.leave_end_date,
            project_group: serviceDataInput.project_group,
            amount: serviceDataInput.amount,
            file: serviceDataInput.file
        });
        setFetching(true);
        if (requestType?.value?.id == '34' && department?.value.id == '4') {
            const formData = new FormData();
            formData.append('id_department', serviceDataInput.department.id);
            formData.append('description', serviceDataInput.description);
            formData.append('priority', serviceDataInput.priority);
            formData.append('status', serviceDataInput.status);
            formData.append('title', serviceDataInput.title);
            formData.append('id_issue_type', serviceDataInput.issues.id);
            formData.append('amount', serviceDataInput.amount);
            formData.append('file', {
                uri: serviceDataInput.file.uri,
                name: serviceDataInput.file.name,
                type: serviceDataInput.file.type
            });
            const response = await fetch(
                `${config.BASE_URL}/service-requests`,
                {
                    method: 'POST',
                    body: formData,
                    headers: {
                        Authorization: `Bearer ${await AsyncStorage.getItem(
                            'AuthToken'
                        )}`,
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );
        } else if (
            requestType?.value?.id == '89' &&
            department?.value.id == '11'
        ) {
            const formData = new FormData();
            formData.append('id_department', serviceDataInput.department.id);
            formData.append('description', serviceDataInput.description);
            formData.append('priority', serviceDataInput.priority);
            formData.append('status', serviceDataInput.status);
            formData.append('title', serviceDataInput.title);
            formData.append('id_issue_type', serviceDataInput.issues.id);
            formData.append('file', {
                uri: serviceDataInput.file.uri,
                name: serviceDataInput.file.name,
                type: serviceDataInput.file.type
            });
            const response = await fetch(
                `${config.BASE_URL}/service-requests`,
                {
                    method: 'POST',
                    body: formData,
                    headers: {
                        Authorization: `Bearer ${await AsyncStorage.getItem(
                            'AuthToken'
                        )}`,
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );
        } else {
            try {

                await httpPost(`/service-requests`, {
                    id_department: serviceDataInput.department.id,
                    priority: serviceDataInput.priority,
                    status: serviceDataInput.status,
                    title: serviceDataInput.title,
                    id_issue_type: serviceDataInput.issues.id,
                    leave_for: serviceDataInput.leaveFor,
                    employeeId: serviceDataInput.employeeId,
                    description: serviceDataInput.description,
                    leave_start_date:
                        serviceDataInput.leaveDates.leave_start_date,
                    leave_end_date: serviceDataInput.leaveDates.leave_end_date,
                    project_group: serviceDataInput.project_group,
                    amount: serviceDataInput.amount
                });
            } catch (err) {
                setFetching(false);
                if (err?.response?.data?.message) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            }
        }

        setFetching(false);
        navigation.navigate(navigationStringText.ServiceRequestsScreenCards, {
            refresh: true
        });
    };
    const getSrDataById = (srId: string) => {
        setFetching(true);
        httpGet(`${apiConstant.SERVICE_REQUEST}?id=${srId}`)
            .then((response: any) => {
                const srData = JSON.parse(response)?.data;
                setServiceData(srData);
                setPriority(
                    priorityData[
                    priorityData.findIndex(
                        (item) =>
                            item.value.toString() ===
                            srData.priority.toString()
                    )
                    ]
                );
                setCurrentStatus(
                    statusData[
                    statusData.findIndex(
                        (item) =>
                            item.value.toString() ===
                            srData?.status.toString()
                    )
                    ]
                );
                getDepartmentById(srData.department.id);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (err?.response?.data?.message) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const handelPostComment = () => {
        setIsAddCommentOpen(!isAddCommentOpen);
        httpPost(`/comments`, {
            id_service_request: serviceData.id,
            comment: comment,
            created_by: serviceData.employeeName,
            id_user: serviceData.userId,
            request_status: serviceData.status
        })
            .then((res) => {
                successToast('Comment Added Successfully');
                setComment('');
                getSrDataById(srId);
            })
            .catch((err) => {
                setFetching(false);

                if (err?.response?.data?.message) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    return (
        <EditServiceRequestsView
            setError={setErrors}
            isFormValid={isFormValid}
            formValidation={formValidation}
            navigation={navigation}
            errorHandlerClicked={errorHandlerClicked}
            setServiceData={setServiceData}
            getDepartmentById={getDepartmentById}
            DeparmentDropDownData={DeparmentDropDownData}
            requestTypeDataFromDB={requestTypeDataFromDB}
            requestTypeData={requestTypeData}
            errors={errors}
            departmentData={departmentData}
            serviceData={serviceData}
            handelUpdateServiceData={handelUpdateServiceData}
            timeAgo={timeAgo}
            department={department}
            setDepartment={setDepartment}
            requestType={requestType}
            setRequestType={setRequestType}
            priority={priority}
            priorityData={priorityData}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerVisibility={errorHandlerVisibility}
            setPriority={setPriority}
            currentStatus={currentStatus}
            setCurrentStatus={setCurrentStatus}
            buttons={buttons}
            selectedIndex={selectedIndex}
            setSelectedIndex={setSelectedIndex}
            startDateOpen={startDateOpen}
            setStartDateOpen={setStartDateOpen}
            endDateOpen={endDateOpen}
            setEndDateOpen={setEndDateOpen}
            startDate={startDate}
            setStartDate={setStartDate}
            endDate={endDate}
            setEndDate={setEndDate}
            statusData={statusData}
            isAddCommentOpen={isAddCommentOpen}
            setIsAddCommentOpen={setIsAddCommentOpen}
            comment={comment}
            selectedFile={selectedFile}
            setSelectedFile={setSelectedFile}
            setComment={setComment}
            handelSave={handelSave}
            serviceDataInput={serviceDataInput}
            setServiceDataInput={setServiceDataInput}
            handelPostComment={handelPostComment}
            fetching={fetching}
        />
    );
};

export default EditServiceRequests;
function dispatch(arg0: any) {
    throw new Error('Function not implemented.');
}

function fetchDRSEmployeeData(arg0: string): any {
    throw new Error('Function not implemented.');
}
