import { View } from 'react-native';
import styles from './style';
import RNText from '../../component/RNText';

export type CommentProps={
        id: number,
        id_service_request: number,
        id_user: number,
        comment: string,
        request_status: number,
        created_at: string,
        created_by: string,
        updated_at: string,
        updated_by: null,
        is_deleted: number,
        deleted_at: null,
        deleted_by: null
}

const ServiceRequetsCommentsCard: React.FC<{
    commentData: CommentProps;
    timeAgo:(date:Date)=>string
}> = ({ commentData,timeAgo }) => {
    
    return (
        <View style={styles.serviceRequestCommentCard}>
           <RNText style={styles.commentDayAndCommenterName}>
            {commentData.created_by} @{timeAgo(new Date(commentData.created_at))} ago
           </RNText>
           <RNText style={styles.commentText}>
            {commentData.comment}
           </RNText>
        </View>
    );
};
export default ServiceRequetsCommentsCard;
