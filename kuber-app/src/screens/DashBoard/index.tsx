import React, { useCallback, useEffect, useState } from 'react';
import { httpGet } from '../../utils/http';
import moment from 'moment';
import { color } from '../../utils/constants/color';
import { stringText } from '../../utils/constants/stringsText';
import { SafeAreaView } from 'react-native';
import apiConstant from '../../utils/constants/apiConstant';
import DashBoardCardView from './dashboardCardView';

export type EmployeeListType = {
    userId: number;
    employeeId: string;
    image: string;
    name: string;
    email: string;
    mobileNumber: string;
    isActive: boolean;
    manager: string;
    designation: string;
};

const DashBoardCardScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
    const [otherData, setOtherData] = useState<any>(null);
    const [listData, setListData] = useState<EmployeeListType[]>([]);
    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [refreshing, setRefreshing] = useState<boolean>(false);
    const [currentPage, setCurrentPage] = useState<number>(1);


    useEffect(() => {
        getHomePage();
        
    }, []);

    const onRefresh = useCallback(() => {
        setRefreshing(true);
        setTimeout(() => {
            setRefreshing(false);
        }, 2000);
    }, []);

    const getHomePage = () => {
        setFetching(true);
        httpGet(`/users/home-page`)
            .then((response: any) => {
                setOtherData(JSON.parse(response)?.data);
                setFetching(false);
                getAllEmployee();
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(
                    true,
                    err?.response?.data?.message ||
                    stringText.SomethingWentwrong
                );
            });
    };
    const getAllEmployee = () => {
        // setFetching(true);
        const startIndex = (currentPage - 1) * 10;
        httpGet(`${apiConstant.ALL_USER}?start=${startIndex}&limit=${10}`)
            .then((response: any) => {
                const empList = JSON.parse(response)?.data?.data;
                if (empList) {
                    setListData(empList);
                }
                setFetching(false);
            })
            .catch((err) => {
                console.error('Fetch error:', err);
                setFetching(false);
                errorHandlerClicked(true, `${stringText.SomethingWentwrong}`);
            });
    };

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <DashBoardCardView
                refreshing={refreshing}
                fetching={fetching}
                errorHandlerVisibility={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
                onRefresh={onRefresh}
                navigation={navigation}
                otherData={otherData}
                employeeList={listData}
            />
        </SafeAreaView>
    );
};

export default DashBoardCardScreen;
