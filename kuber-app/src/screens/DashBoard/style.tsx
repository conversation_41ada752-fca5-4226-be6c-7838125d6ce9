import { StyleSheet } from "react-native";
import { color } from "../../utils/constants/color";

const styles = StyleSheet.create({
    parentContainer: {
        padding: 10,
        gap:10,
        height:'100%'
    },
    cardView: {
        backgroundColor: color.WHITE,
        padding: 16,
        marginBottom: 10,
        width: '100%',
        borderRadius: 8
    },
    cardHeadingView: {
        width: '100%',
        flexDirection: 'row',
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    cardHeadingViewProject: {
        width: '100%',
        flexDirection: 'row',
        alignContent: 'center',
        alignItems: 'center',
    },
    cardHeading: {
        fontWeight: '600',
        fontSize: 20,
        marginLeft: 15,
    },
    text: {
        fontSize: 14,
        color: color.BLACK,
    },
    redText: {
        color: color.DARK_RED
    },
    touchableText: {
        fontSize: 16,
        color: color.ACCENT_BLUE,
        textDecorationLine:'underline',
        width:140,
    },
    rowDirection: {
        flexDirection: 'row'
    },
    valueView: { 
        gap: 5, 
        marginTop: 10 ,
    },
    employeeListView: {
        marginTop:-12,
        backgroundColor: color.WHITE,
        paddingHorizontal: 16,
        width: '100%',
        maxHeight:250,
        borderBottomRightRadius:8,
        borderBottomLeftRadius:8,
    },
    employeeCard: {
        padding: 10,
        marginVertical: 5,
        backgroundColor: color.WHITE,
        borderRadius: 5,
    },
});

export default styles;