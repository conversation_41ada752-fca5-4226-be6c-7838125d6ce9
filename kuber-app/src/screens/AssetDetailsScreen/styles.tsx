import { StyleSheet } from "react-native";
import { color } from '../../utils/constants/color'


const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginHorizontal:14,
        margin:5,
        flexDirection:'row'
    },
    laptopContainer: {
        flexDirection: 'row',
        opacity: 1,
        borderTopLeftRadius:6,
        borderBottomLeftRadius:6,
        backgroundColor: color.WHITE,
        shadowColor: color.GREAYSCALE,
        width:'35%',
        columnGap: 25,
        shadowOffset: {
            width: 1,
            height: 1.8,
        },
        shadowOpacity: 1,
        shadowRadius: 6,
        elevation: 10
    },
    laptopIcon: {
        height: "100%",
        width: "100%"
    },
    textContainer:{
        width: '65%', 
        backgroundColor:color.WHITE,
        paddingVertical:10,
        paddingLeft:10,
        paddingRight:5,
        borderTopRightRadius:6,
        borderBottomRightRadius:6
    }

})

export default styles