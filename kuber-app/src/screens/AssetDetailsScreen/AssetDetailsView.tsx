import React from "react";
import { View } from "react-native";
import RNText from "../../component/RNText";
import styles from "./styles";
import { SafeAreaView } from "react-native";
import { stringText } from "../../utils/constants/stringsText";
import RNImage from "../../component/RNImage";
import imageConstant from "../../utils/constants/imageConstant";




type AssetDetailsScreen = {
    navigation: any
}

const AssetDetailsCard: React.FC<{
    laptopName: string;
    laptop_no: string;
    ram_size: string;
    hard_disk_size: string;
    assigned_on: string;
}> = ({ laptopName, laptop_no, ram_size, hard_disk_size, assigned_on }) => {
    return (
        <SafeAreaView>
            <View style={styles.container}>
                <View style={styles.laptopContainer}>
                    <RNImage
                        source={imageConstant.AssetIcon}
                        style={styles.laptopIcon}
                        resizeMode={'contain'}
                    />
                </View>
                <View style={styles.textContainer}>
                    <RNText>{stringText.LaptopId} : {laptop_no}</RNText>
                    <RNText>{stringText.Ram} : {ram_size}</RNText>
                    <RNText>{stringText.HardDisk} : {hard_disk_size}</RNText>
                    <RNText>{stringText.Make} : {laptopName}</RNText>
                    <RNText>{stringText.AssignedOn} : {assigned_on}</RNText>
                </View>
            </View>
        </SafeAreaView>
    )
}

export default AssetDetailsCard;