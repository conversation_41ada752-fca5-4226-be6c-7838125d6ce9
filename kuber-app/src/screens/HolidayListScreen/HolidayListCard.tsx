import React from "react"
import { SafeAreaView, View } from "react-native"
import RNText from "../../component/RNText"
import styles from "./styles"

const getAllMonth = [
    { label: "Jan", value: 1 },
    { label: "Feb", value: 2 },
    { label: "Mar", value: 3 },
    { label: "April", value: 4 },
    { label: "May", value: 5 },
    { label: "June", value: 6 },
    { label: "July", value: 7 },
    { label: "Aug", value: 8 },
    { label: "Sept", value: 9 },
    { label: "Oct", value: 10 },
    { label: "Nov", value: 11 },
    { label: "Dec", value: 12 },
]

const HolidayListCard: React.FC<{
    title: string;
    date: string;
}> = ({ title, date }) => {
    const dateObj = new Date(date);
    // const formattedDate = `${dateObj.getMonth() + 1}/${dateObj.getDate()}/${dateObj.getFullYear()}`;
    const DateDay = `${dateObj.getDate()}`;
    const DateMonth = `${dateObj.getMonth() + 1}`;
    const daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    const dayIndex = dateObj.getDay();
    const dayOfWeek = daysOfWeek[dayIndex];

    return (
        <SafeAreaView>
            <View style={styles.parentCard}>
                <View style={styles.dateBlock}>
                    <RNText style={styles.dayText}>{DateDay}</RNText>
                    <RNText style={styles.monthText}>{getAllMonth.find(month => month.value.toString() === DateMonth)?.label}</RNText>
                </View>
                <View style={styles.verticalLine} />
                <View style={styles.titleBlock}>
                    <RNText style={styles.titleText}>{title}</RNText>
                    <RNText style={styles.weekDayText}>{dayOfWeek}</RNText>
                </View>
            </View>
        </SafeAreaView>
    );
};

export default HolidayListCard;