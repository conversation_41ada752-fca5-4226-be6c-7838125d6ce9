import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    mainView: {
        backgroundColor: color.BACKGROUND_COLOR,
        height: '100%',
        width: '100%',
        padding: 20
    },
    imageView: {
        height: 65,
        width: 166,
        alignSelf: 'center',
        marginTop: 90
    },
    emailId: {
        flex: 1,
        color: color.GREY_COLOR,
        padding: 10
    },
    passWord: {
        flex: 1,
        color: color.GREY_COLOR,
        padding: 10
    },
    Button: {
        borderWidth: 1,
        padding: 10,
        width: '100%',
        backgroundColor: color.DARK_BLUE,
        borderRadius: 5,
        borderColor: color.DARK_BLUE
    },
    textButton: {
        textAlign: 'center',
        color: color.WHITE,
        fontFamily: 'Poppins-Regular',
        fontSize: 18
    },
    MarginView: {
        marginTop: 20
    },
    logButton: {
        marginTop: 75
    },
    logInWithGoogleButton:{
        marginTop:'20%',
        marginBottom:'30%'
    },
    textHead: {
        marginBottom: 6,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-Regular',
        fontSize: 14
    },
    otherLogo: {
        marginTop: 80
    },
    textPass: {
        color: color.GREAYSCALE,
        paddingVertical: 5,
        fontStyle: 'italic',
        fontSize: 12,
        fontFamily: 'Poppins-Regular'
    },
    EmailIcon: {
        width: 20,
        height: 16,
        marginLeft: 8
    },
    EmailInput: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: color.WHITE,
        borderWidth: 1,
        borderColor: color.DARK_BLUE,
        height: 40,
        borderRadius: 5
    },
    PasswordIcon: {
        width: 16,
        height: 21,
        marginLeft: 8
    },
    copyRightText: {
        color: color.BLACK,
        fontFamily: 'Poppins-Regular',
        fontSize: 12,
    },
    icon: {
        width: 27,
        height: 27,
        marginRight: 5
      },
});

export default styles;
