import React, { useMemo, useState } from 'react';
import {
    SafeAreaView,
    ScrollView,
    View,
    FlatList,
    StyleSheet
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import RNText from '../../component/RNText';
import { styles } from './styles';
import { color } from '../../utils/constants/color';
import { stringText } from '../../utils/constants/stringsText';
import RNButton from '../../component/RNButton';

type EducationalInfo = {
    course_of_study: string;
    degree: string;
    graduated: string;
    id: number;
    id_qualification_set: number;
    id_user: number;
    percentage: string;
    skill_name: number;
    year: string;
};

type Props = {
    data: any;
    fetching: boolean;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
    qualificationSet: any;
    qualificationSkill: any;
};

const BackgroundInfoView: React.FC<Props> = ({
    data,
    fetching,
    errorHandlerVisibility,
    errorHandlerMessage,
    errorHandlerClicked,
    qualificationSet,
    qualificationSkill
}) => {
    const [selectedFieldIndex, setSelectedFieldIndex] = useState<number | null>(0);

    const handleFieldClick = (index: number) => {
        if (selectedFieldIndex === index) {
            setSelectedFieldIndex(null);
        } else {
            setSelectedFieldIndex(index);
        }
    };

    // Preprocess the data to group by id_qualification_set
    const groupedData = useMemo(() => {
        const groups: { [key: number]: EducationalInfo[] }  = {};
        data?.educationalInfo?.forEach((item:any) => {
            if (!groups[item.id_qualification_set]) {
                groups[item.id_qualification_set] = [];
            }
            groups[item.id_qualification_set].push(item);
        });
        return Object.values(groups);
    }, [data]);

    const renderItem = ({ item, index }: { item: any, index: number }) => {
        return (
            <View key={index} style={{ marginBottom: 15 }}>
                <RNButton
                    handleOnPress={() => handleFieldClick(index)}
                    style={[
                        styles.tableTopTitleTextWrapper,
                        {
                            borderBottomRightRadius: selectedFieldIndex === index ? 0 : 6,
                            borderBottomLeftRadius: selectedFieldIndex === index ? 0 : 6,
                        }
                    ]}
                    ActiveOpacity={0.9}
                >
                    <View style={styles.viewContainer}>
                        <RNText style={styles.cardTitle}>
                            {
                                qualificationSet[
                                    qualificationSet.findIndex(
                                        (set: any) =>
                                            set.id ===
                                            item[0].id_qualification_set
                                    )
                                ]?.qualification
                            }
                        </RNText>
                        <Ionicons
                            name={selectedFieldIndex === index ? 'chevron-up' : 'chevron-down'}
                            size={20}
                            color={color.BLACK}
                        />
                    </View>
                </RNButton>
                {selectedFieldIndex === index && (
                    <>
                        {item.map((subItem:any, subIndex:number) => (
                            <View key={subIndex}>
                                {subIndex > 0 && <View style={styles.horizontalLine} />}
                                <View style={styles.seletedCard}>
                                    <View style={styles.rowGap}>
                                        <RNText
                                            style={styles.lebelStyle}
                                        >
                                            {stringText.QualificationOrSkillset}
                                        </RNText>
                                        <RNText
                                            style={styles.infoField}
                                        >
                                            {
                                                qualificationSkill[
                                                    qualificationSkill?.findIndex(
                                                        (set: any) =>
                                                            set.id ===
                                                            subItem.skill_name
                                                    )
                                                ]?.qualification_skill_name
                                            }
                                        </RNText>
                                    </View>

                                    <View style={styles.rowGap}>
                                        <RNText
                                            style={styles.lebelStyle}
                                        >
                                            {stringText.Graduated}
                                        </RNText>
                                        <RNText
                                            style={styles.infoField}
                                        >
                                            {subItem.graduated}
                                        </RNText>
                                    </View>

                                    <View style={styles.rowGap}>
                                        <RNText
                                            style={styles.lebelStyle}
                                        >
                                            {stringText.PassingYear}
                                        </RNText>
                                        <RNText
                                            style={styles.infoField}
                                        >
                                            {subItem.year}
                                        </RNText>
                                    </View>

                                    <View style={styles.rowGap}>
                                        <RNText
                                            style={styles.lebelStyle}
                                        >
                                            {stringText.CollegeUniversity}
                                        </RNText>
                                        <RNText
                                            style={styles.infoField}
                                        >
                                            {subItem.degree}
                                        </RNText>
                                    </View>

                                    <View style={styles.rowGap}>
                                        <RNText
                                            style={styles.lebelStyle}
                                        >
                                            {stringText.BranchSubject}
                                        </RNText>
                                        <RNText
                                            style={styles.infoField}
                                        >
                                            {subItem.course_of_study}
                                        </RNText>
                                    </View>

                                    <View style={styles.rowGap}>
                                        <RNText
                                            style={styles.lebelStyle}
                                        >
                                            {stringText.PercentageCgpa}
                                        </RNText>
                                        <RNText
                                            style={[styles.infoField, { paddingLeft: 0 }]}
                                        >   {subItem.percentage}
                                        </RNText>
                                    </View>
                                </View>
                            </View>
                        ))}
                    </>
                )}
            </View>
        );
    };

    return (
        <SafeAreaView>
            <ScrollView>
                <View style={styles.mainContainer}>
                    <FlatList
                        data={groupedData}
                        renderItem={renderItem}
                        keyExtractor={(item, index) => index.toString()}
                    />
                    <ErrorHandlerPopup
                        visible={errorHandlerVisibility}
                        errorHandlerMessage={errorHandlerMessage}
                        errorHandlerClicked={errorHandlerClicked}
                    />
                </View>
            </ScrollView>
        </SafeAreaView>
    ); 
};

export default BackgroundInfoView;
