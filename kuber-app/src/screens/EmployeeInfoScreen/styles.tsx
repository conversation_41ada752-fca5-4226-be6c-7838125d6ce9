import { Platform, StyleSheet } from "react-native";
import { color } from '../../utils/constants/color'


const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    rowGap: 15
  },
  employeeTxt: {
    fontWeight: 'bold',
    color: color.ACCENT_BLUE,
    textAlign: 'center'
  },
  Info: {
    backgroundColor: color.WHITE,
    padding: 10,
    borderRadius: 5,
    rowGap: 20,
    paddingVertical:20
  },
  infoField: {
    borderWidth: 0.5,
    borderColor: color.GREAYSCALE,
    padding: Platform.OS=='ios'? 16:10,
    borderRadius: 8,
    color:color.BLACK,
    height:55,
    verticalAlign:'middle',
  },
  dropdown: {
    height:50,
    borderColor: color.GREAYSCALE,
    borderWidth: 0.5,
    borderRadius: 8,
    padding: 15,
  },
  icon: {
    marginRight: 5,
  },
  label: {
    position: 'absolute',
    backgroundColor: color.WHITE,
    left: 10,
    top: -10,
    zIndex: 999,
    paddingHorizontal: 4,
    fontSize: 14,
    color:color.GREAYSCALE

  },
  placeholderStyle: {
    fontSize: 16,
    color:color.BLACK
  },
  selectedTextStyle: {
    fontSize: 14,
    color:color.BLACK
  },
  iconStyle: {
    width: 0,
    height: 0,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
  titleText:{
    color:color.GREAYSCALE,
    paddingLeft:5
  },
  radioButton:{
    color:color.GREAYSCALE,
    marginRight: 20 
  }
})

export default styles