import EmployeeInfoView from "./BasicInfoCard";
import EmployeeInfoViewTab2 from "./ProfessionalInfoCard";
import EmployeeInfoViewTab3 from "./OtherInfoCard";
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { color } from "../../utils/constants/color";

const Tab = createMaterialTopTabNavigator();

export type Props = {
    navigation: any
}

export type EmployeeInfoType = {
    designation: any
    designationBand: any
    manager: any
    agreement: any
    employeeInfo: any
}

const EmployeeInfoScreen = () => {

    return (
        <Tab.Navigator
            screenOptions={{
                tabBarLabelStyle: { fontSize: 12 },
                tabBarActiveTintColor: color.DARK_BLUE,
                tabBarInactiveTintColor: color.GREAYSCALE,
            }}
        >
            <Tab.Screen name="Basic" component={EmployeeInfoView} />
            <Tab.Screen name="Professional" component={EmployeeInfoViewTab2} />
            <Tab.Screen name="Other" component={EmployeeInfoViewTab3} />
        </Tab.Navigator>
    )
}
export default EmployeeInfoScreen;