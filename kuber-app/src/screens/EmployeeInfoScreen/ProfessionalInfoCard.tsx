import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>reaView, <PERSON>rollView, View } from "react-native";
import RNText from "../../component/RNText";
import styles from "./styles";
import { stringText } from "../../utils/constants/stringsText";
import { RadioButton } from 'react-native-paper';
import { Dropdown } from "react-native-element-dropdown";
import { color } from "../../utils/constants/color";
import AntDesign from "react-native-vector-icons/AntDesign"
import RNActivityIndicator from '../../component/Loader';
import { httpGet } from "../../utils/http";
import apiConstant from "../../utils/constants/apiConstant";

type EmployeeInfoScreen = {
    navigation: any
}

const serviceAgreementData = [
    {label: "24 Month", value: 1},
    {label: "18 Month", value: 2},
    {label: "12 Month", value: 3},

]

const typeOfRelievingDropDown = [
    { label: "Select", value: 0 },
    { label: "Terminated", value: 1 },
    { label: "Absconded", value: 2 },
    { label: "Relieved", value: 3 }
]
const getAllWeek = [
    { label: 'Sunday', value: 'Sunday' },
    { label: 'Monday', value: 'Monday' },
    { label: 'Tuesday', value: 'Tuesday' },
    { label: 'Wednesday', value: 'Wednesday' },
    { label: 'Thursday', value: 'Thursday' },
    { label: 'Friday', value: 'Friday' },
    { label: 'Saturday', value: 'Saturday' }
]

const getAllWorkingExperience = [
    { label: "0 - 0.5 Year", value: 0 },
    { label: "0.5 - 1 Year", value: 1 },
    { label: "1 - 2 Years", value: 2 },
    { label: "2 - 5 Years", value: 3 },
    { label: "5 - 10 Years", value: 4 },
    { label: "10+ Years", value: 5 }
]

const getAllNoticePeriod = [
    { label: "1 Month", value: 1 },
    { label: "2 Month", value: 2 },
    { label: "3 Month", value: 3 },
    { label: "4 Month", value: 4 },
    { label: "5 Month", value: 5 },
    { label: "6 Month", value: 6 }
]

const getAllTimingData = [
    { label: "7:00 AM", value: 2 },
    { label: "9:00 AM", value: 3 },
    { label: "10:30 AM", value: 4 },
    { label: "11:00 AM", value: 5 },
    { label: "11:30 AM", value: 6 },
    { label: "2:00 PM", value: 7 },
    { label: "4:00 PM", value: 8 },
    { label: "1:00 AM", value: 9 },
    { label: "9:30 AM", value: 10 },
    { label: "10:00 AM", value: 11 },
    { label: "6:00 PM", value: 15 },
    { label: "1:00 PM", value: 16 },
    { label: "8:00 PM", value: 20 },
    { label: "1:30 PM", value: 28 },
    { label: "9:30 PM", value: 29 },
    { label: "12:00 PM", value: 33 },
    { label: "10:30 PM", value: 34 },
    { label: "8:30 PM", value: 36 },
    { label: "7:30 AM", value: 37 },
    { label: "9:00 PM", value: 41 },
    { label: "3:30 PM", value: 42 },
    { label: "12:10 AM", value: 43 },
    { label: "5:00 PM", value: 44 },
    { label: "8:30 AM", value: 45 },
    { label: "6:30 PM", value: 46 }
]

const EmployeeInfoViewTab2: React.FC<EmployeeInfoScreen> = () => {
    const [designation, setDesignation] = useState()
    const [designationDropDown, setDesignationDropDown] = useState<[]>([])
    const [designationBand, setDesignationBand] = useState('')
    const [designationBandDropDown, setDesignationBandDropDown] = useState<[]>([])
    const [manager, setManager] = useState('')
    const [managerDropDown, setManagerDropDown] = useState<[]>([])
    const [currentManager , setCurrentManger] = useState<string>('')
    const [firstWeekend, setFirstWeekend] = useState('')
    const [secondWeekend, setSecondWeekend] = useState('')
    const [experience, setExperience] = useState(null)
    const [agreementDropDown, setAgreementDropDown] = useState<[]>([])
    const [agreement, setAgreement] = useState('')
    const [noticePeriod, setNoticePeriod] = useState('')
    const [typeOfRelieving, setTypeOfRelieving] = useState(null)
    const [underNotice, setUnderNotice] = useState('')

    const [isFocusDesignation, setIsFocusDesignation] = useState(false);
    const [isFocusDesignationBand, setIsFocusDesignationBand] = useState(false);
    const [isFocusManager, setIsFocusManager] = useState(false)
    const [isFocusFirstWeekend, setIsFocusFirstWeekend] = useState(false)
    const [isFocusSecondWeekend, setIsFocusSecondWeekend] = useState(false)
    const [isFocusExperience, setIsFocusExperience] = useState(false)
    const [isFocusAgreement, setIsFocusAgreement] = useState(false)
    const [isFocusNoticePeriod, setIsFocusNoticePeriod] = useState(false)
    const [isFocusTypeRelieving, setIsFocusTypeRelieving] = useState(false)

    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
    const [employeeInfo, setEmployeeInfo] = useState<any>()

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    const formatDate = (inputDate: string) => {
        const date = new Date(inputDate);
      
        const day = date.getUTCDate();
        const month = date.getUTCMonth() + 1;
        const year = date.getUTCFullYear();
      
        const formattedDay = day < 10 ? `0${day}` : day;
        const formattedMonth = month < 10 ? `0${month}` : month;
      
        const formattedDate = `${formattedMonth}/${formattedDay}/${year}`;

      
        return formattedDate;
      }

    const RenderDesignation = () => {
        if (designation || isFocusDesignation) {
            return (
                <RNText style={[styles.label, isFocusDesignation && { color: color.ACCENT_BLUE }]}>
                    Designation
                </RNText>
            );
        }
        return null;
    };

    const RenderDesignationBand = () => {
        if (designationBand || isFocusDesignationBand) {
            return (
                <RNText style={[styles.label, isFocusDesignationBand && { color: color.ACCENT_BLUE }]}>
                    Designation Band
                </RNText>
            );
        }
        return null;
    };

    const RenderManger = () => {
        if (manager || isFocusManager) {
            return (
                <RNText style={[styles.label, isFocusManager && { color: color.ACCENT_BLUE }]}>
                    Manager
                </RNText>
            );
        }
        return null;
    };

    const RenderFirstWeekend = () => {
        if (firstWeekend || isFocusFirstWeekend) {
            return (
                <RNText style={[styles.label, isFocusFirstWeekend && { color: color.ACCENT_BLUE }]}>
                    First Weekend
                </RNText>
            );
        }
        return null;
    };

    const RenderSecondWeekend = () => {
        if (secondWeekend || isFocusSecondWeekend) {
            return (
                <RNText style={[styles.label, isFocusSecondWeekend && { color: color.ACCENT_BLUE }]}>
                    Second Weekend
                </RNText>
            );
        }
        return null;
    };

    const RenderExperienceWhileJoining = () => {
        if (experience != null || isFocusExperience) {
            return (
                <RNText style={[styles.label, isFocusExperience && { color: color.ACCENT_BLUE }]}>
                    Experience While Joining
                </RNText>
            );
        }
        return null;
    };

    const RenderServiceAgreementPeriod = () => {
        if (agreement || isFocusAgreement) {
            return (
                <RNText style={[styles.label, isFocusAgreement && { color: color.ACCENT_BLUE }]}>
                    Service Agreement Period
                </RNText>
            );
        }
        return null;
    };

    const RenderNoticePeriod = () => {
        if (noticePeriod || isFocusNoticePeriod) {
            return (
                <RNText style={[styles.label, isFocusNoticePeriod && { color: color.ACCENT_BLUE }]}>
                    Notice Period
                </RNText>
            );
        }
        return null;
    };

    const RenderTypeOfRelieving = () => {
        if (typeOfRelieving != null || isFocusTypeRelieving) {
            return (
                <RNText style={[styles.label, isFocusTypeRelieving && { color: color.ACCENT_BLUE }]}>
                    Type Of Relieving
                </RNText>
            );
        }
        return null;
    };

    useEffect(() => {
        setFetching(true)
        httpGet(apiConstant.DESIGNATION)
            .then((response: any) => {
                const designationData = JSON.parse(response)?.data;
                if (designationData) {
                    setDesignationDropDown(designationData)
                }
                setFetching(false)
            }
            ).catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            })
    }, []);

    useEffect(() => {
        setFetching(true)
        httpGet(apiConstant.DESIGNATION_BAND)
            .then((response: any) => {
                const designationBandData = JSON.parse(response)?.data;
                if (designationBandData) {
                    setDesignationBandDropDown(designationBandData)
                }
                setFetching(false)
            }
            ).catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            })
    }, []);

    useEffect(() => {
        setFetching(true)
        httpGet(apiConstant.SERVICE_AGREEMENT)
            .then((response: any) => {
                const agreementData = JSON.parse(response)?.data;
                if (agreementData) {
                    setAgreementDropDown(agreementData)
                }
                setFetching(false)
            }
            ).catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            })
    }, []);

    useEffect(() => {
        setFetching(true)
        httpGet(apiConstant.EMP_INFO)
            .then((response: any) => {
                const employeeInfodata = JSON.parse(response)?.data;
                if (employeeInfodata) {
                    setEmployeeInfo(employeeInfodata);
                    setDesignation(employeeInfodata.id_grade);
                    setDesignationBand(employeeInfodata.designation_band);
                    setManager(employeeInfodata.id_manager);
                    setFirstWeekend(employeeInfodata.first_weekend);
                    setSecondWeekend(employeeInfodata.second_weekend);
                    setExperience(employeeInfodata.working_experience);
                    setAgreement(employeeInfodata.id_service_agreement);
                    setUnderNotice(employeeInfodata.underNotice.toString());
                    setNoticePeriod(employeeInfodata.noticePeriod);
                    setTypeOfRelieving(employeeInfodata.termination_type);
                }
                setFetching(false);
            }
            ).catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            })        
    }, [])

    useEffect(()=>{
        httpGet(`${apiConstant.USER_ID}/${manager}`)
            .then((response: any) => {
                const managerName = JSON.parse(response)?.data;
                if (managerName) {
                    setCurrentManger(managerName.name)
                }
                setFetching(false);
            }
            ).catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            })
    },[manager])

    return (
        <SafeAreaView>
            <ScrollView>
                <View style={styles.container}>
                    <View style={[styles.Info,{rowGap:20}]}>
                        <View style={{ position: 'relative' }}>
                            <RenderDesignation />
                            <Dropdown
                                style={[styles.dropdown, isFocusDesignation && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                selectedTextProps={{numberOfLines:1}}
                                data={designationDropDown.map((item: any) => ({ label: item.grade_name + " - " + item.desc, value: item.id }))}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusDesignation ? 'Designation' : '...'}
                                value={designation}
                                disable={true}
                                onFocus={() => setIsFocusDesignation(true)}
                                onBlur={() => setIsFocusDesignation(false)}
                                onChange={item => {
                                    setDesignation(item.value);
                                    setIsFocusDesignation(false);
                                }}
                            />
                        </View>

                        <View style={{ position: 'relative' }}>
                            <RenderDesignationBand />
                            <Dropdown
                                style={[styles.dropdown, isFocusDesignationBand && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                data={designationBandDropDown.map((item: any) => ({ label: item.band_title, value: item.id }))}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusDesignationBand ? 'Designation Band' : '...'}
                                value={designationBand}
                                disable={true}
                                onFocus={() => setIsFocusDesignationBand(true)}
                                onBlur={() => setIsFocusDesignationBand(false)}
                                onChange={item => {
                                    setDesignationBand(item.value);
                                    setIsFocusDesignationBand(false);
                                }}
                            />
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {stringText.Manager}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {currentManager}
                            </RNText>
                        </View>
                        <View style={{ position: 'relative' }}>
                            <RenderFirstWeekend />
                            <Dropdown
                                style={[styles.dropdown, isFocusFirstWeekend && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                data={getAllWeek}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusFirstWeekend ? 'First Weekend' : '...'}
                                value={firstWeekend}
                                disable={true}
                                onFocus={() => setIsFocusFirstWeekend(true)}
                                onBlur={() => setIsFocusFirstWeekend(false)}
                                onChange={item => {
                                    setFirstWeekend(item.value);
                                    setIsFocusFirstWeekend(false);
                                }}
                            />
                        </View>
                        <View style={{ position: 'relative' }}>
                            <RenderSecondWeekend />
                            <Dropdown
                                style={[styles.dropdown, isFocusSecondWeekend && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                data={getAllWeek}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusSecondWeekend ? 'Second Weekend' : '...'}
                                value={secondWeekend}
                                disable={true}
                                onFocus={() => setIsFocusSecondWeekend(true)}
                                onBlur={() => setIsFocusSecondWeekend(false)}
                                onChange={item => {
                                    setSecondWeekend(item.value);
                                    setIsFocusSecondWeekend(false);
                                }}
                            />
                        </View>
                        <View style={{ gap: 5 }}>
                        <RNText style={styles.label}>
                            {stringText.HireData}
                        </RNText>
                        <RNText style={styles.infoField}>
                            {employeeInfo?.hire_date ? formatDate(employeeInfo?.hire_date) : "NA"}
                        </RNText>
                        </View>
                        <View style={{ position: 'relative' }}>
                            <RenderExperienceWhileJoining />
                            <Dropdown
                                style={[styles.dropdown, isFocusExperience && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                data={getAllWorkingExperience}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusExperience ? 'Experience While Joining' : '...'}
                                value={experience}
                                disable={true}
                                onFocus={() => setIsFocusExperience(true)}
                                onBlur={() => setIsFocusExperience(false)}
                                onChange={(item: any) => {
                                    setExperience(item.value);
                                    setIsFocusExperience(false);
                                }}
                            />
                        </View>
                        <View style={{ position: 'relative' }}>
                            <RenderServiceAgreementPeriod />
                            <Dropdown
                                style={[styles.dropdown, isFocusAgreement && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                data={serviceAgreementData}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusAgreement ? 'Service Agreement Period' : '...'}
                                value={agreement}
                                disable={true}
                                onFocus={() => setIsFocusAgreement(true)}
                                onBlur={() => setIsFocusAgreement(false)}
                                onChange={(item: any) => {
                                    setAgreement(item.id);
                                    setIsFocusAgreement(false);
                                }}
                            />
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {stringText.ServiceAgreementAmount}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {employeeInfo?.service_agreement_amount ? employeeInfo?.service_agreement_amount : "NA"}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.titleText}>
                                {stringText.UnderNotice}
                            </RNText>
                            <RadioButton.Group
                                onValueChange={newValue => setUnderNotice(newValue)}
                                value={underNotice}
                            >
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <RadioButton.Item
                                      mode="android"
                                      label="Yes"
                                      position="leading"
                                      style={{
                                          marginLeft: -15,
                                          marginBottom: -5
                                      }}
                                    
                                    value="1" disabled={true} />
                                   
                                    <RadioButton.Item
                                      mode="android"
                                      label="No"
                                      position="leading"
                                      style={{
                                          marginLeft: -15,
                                          marginBottom: -5
                                      }}
                                    
                                    value="0" disabled={true} />
                                    
                                </View>
                            </RadioButton.Group>
                        </View>
                        <View style={{ position: 'relative' }}>
                            <RenderNoticePeriod/>
                            <Dropdown
                                style={[styles.dropdown, isFocusNoticePeriod && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                data={getAllNoticePeriod}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusNoticePeriod ? 'Notice Period' : '...'}
                                value={noticePeriod}
                                disable={true}
                                onFocus={() => setIsFocusNoticePeriod(true)}
                                onBlur={() => setIsFocusNoticePeriod(false)}
                                onChange={(item: any) => {
                                    setNoticePeriod(item.value);
                                    setIsFocusNoticePeriod(false);
                                }}
                            />
                        </View>
                        <View style={{ position: 'relative' }}>

                            <RenderTypeOfRelieving />
                            <Dropdown
                                style={[styles.dropdown, isFocusTypeRelieving && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                data={typeOfRelievingDropDown}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusTypeRelieving ? 'Type Of Relieving' : '...'}
                                value={typeOfRelieving}
                                disable={true}
                                onFocus={() => setIsFocusTypeRelieving(true)}
                                onBlur={() => setIsFocusTypeRelieving(false)}
                                onChange={(item: any) => {
                                    setTypeOfRelieving(item.value);
                                    setIsFocusTypeRelieving(false);
                                }}
                            />
                        </View>
                    </View>

                </View>
            </ScrollView>
        <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    )
}
export default EmployeeInfoViewTab2;
