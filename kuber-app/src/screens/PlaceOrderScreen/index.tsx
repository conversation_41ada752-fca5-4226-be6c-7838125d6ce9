import React, { useEffect, useState } from 'react';
import { httpPost } from '../../utils/http';
import GetLocation from 'react-native-get-location';
import moment from 'moment';
import PlaceOrderView from './PlaceOrderView';
import { stringText } from '../../utils/constants/stringsText';
import AsyncStorage from '@react-native-async-storage/async-storage';

type Props = {
    navigation: any;
    route: any;
};

const PlaceOrder = (props: Props) => {
    const { navigation } = props;
    const [food, setFood] = useState<string>('');
    const [loader, setLoader] = useState<boolean>(false);
    const [datetoday, setDatetoday] = useState<Date>(new Date());
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
    const [isblurQRcode, setIsblurQRcode] = useState<boolean>(false);
    const [orderstatusCompleted, setOrderstatusCompleted] = useState<string>('');

    let locationData: any = {};

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        GoToOrder();
    }, []);

    // useEffect(() => {
    //     setFood(foodOrderData?.file_name);
    //     setOrderstatusCompleted(
    //         foodOrderData?.status
    //     );
    //     setLoader(isLoading);
    // }, [foodOrderData,isLoading])

    // useEffect(()=>{
    //     if (error){
    //         errorHandlerClicked(true,error?.message)
    //     }
    // },[error])

    const GoToOrder = async () => {
        const userVersion = await AsyncStorage.getItem("user_version");
        const playstoreVersion = await AsyncStorage.getItem("playstore_version");
        setLoader(true);
        setFood("");
        GetLocation.getCurrentPosition({
            enableHighAccuracy: true,
            timeout: 60000
        })
            .then((location) => {
                const payload = {
                    order_type: '2',
                    order_date: `${moment(datetoday).format('YYYY-MM-DD')}`,
                    latitude: `${location?.latitude}`,
                    longitude: `${location?.longitude}`,
                    user_version: userVersion?.toString(),
                    playstore_version: playstoreVersion?.toString()
                };
                // dispatch(postFoodOrderData({ url: '/generate_food_qr', data: payload }) as any)
                httpPost('/generate_food_qr', payload)
                    .then(async (response: any) => {
                        setFood(JSON.parse(response)?.data?.file_name);
                        setOrderstatusCompleted(
                            JSON.parse(response)?.data?.status
                        );
                        setLoader(false);
                    })
                    .catch((err) => {
                        setLoader(false);
                        if (
                            err?.response?.data?.message !== undefined &&
                            err?.response?.data?.message !== null &&
                            err?.response?.data?.message !== ''
                        ) {
                            errorHandlerClicked(
                                true,
                                err?.response?.data?.message
                            );
                        } else {
                            errorHandlerClicked(
                                true,
                                `${stringText.SomethingWentwrong}`
                            );
                        }
                        setIsblurQRcode(true);
                    });
                return location;
            })
            .catch(() => {
                setLoader(false);
                errorHandlerClicked(
                    true,
                    `${stringText.ConfirmLocation}`
                );
                setIsblurQRcode(true);
            });
    };

    return (
        <PlaceOrderView
            orderstatusCompleted={orderstatusCompleted}
            isblurQRcode={isblurQRcode}
            loader={loader}
            food={food}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
        />
    );
};

export default PlaceOrder;