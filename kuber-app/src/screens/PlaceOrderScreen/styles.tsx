import { StyleSheet } from "react-native";
import { color } from "../../utils/constants/color";

const styles = StyleSheet.create({
    mainView: {
        flex: 1,
        backgroundColor: color.OFF_WHITE
    },
    containerView: {
        flexDirection: 'row',
        margin: 16
    },
    textOrder: {
        marginLeft: 16,
        marginRight: 16,
        fontSize: 14,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-Regular',
        margin: 20
    },
    Img: {
        borderWidth: 2,
        borderColor: color.DARK_BLUE,
        width: 355,
        height: 355,
        alignSelf: 'center',
        borderRadius: 4,
        backgroundColor: color.WHITE,
        marginTop: 10,
        justifyContent: 'center',
        alignItems: 'center'
    },
    ImgView: {
        alignSelf: 'center',
        height: 255,
        width: 255
    },
    TextImg1: {
        flexDirection: 'column',
        justifyContent: 'flex-start'
    },
    inputText: {
        borderWidth: 1,
        borderColor: color.DARK_BLUE,
        width: 120,
        height: 40,
        borderRadius: 5,
        backgroundColor: color.WHITE,
        marginTop: 20,
        marginLeft: 16
    },
    calenderView: {
        padding: 10,
        height: 40,
        width: 50,
        marginLeft: 5
    },
    CalenderText: {
        marginLeft: 16,
        fontSize: 16,
        color: color.GREY_COLOR
    },
    textOrderHead: {
        margin: 16,
        color: color.GREY_COLOR,
        fontSize: 14,
        fontFamily: 'Poppins-SemiBold'
    },
    HederTittle: {
        fontSize: 14,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-Regular'
    },
    HederView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    }
});

export default styles;