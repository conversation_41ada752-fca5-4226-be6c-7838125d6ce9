import React from 'react';
import { View } from 'react-native';
import RNText from '../../component/RNText';
import RNImage from '../../component/RNImage';
import styles from './styles';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import { stringText } from '../../utils/constants/stringsText';
import RNButton from '../../component/RNButton';

const ManegerCard: React.FC<{
    empData: any;
    navigation: any;
    handelSetId: any;
}> = ({ empData, navigation, handelSetId }) => {
    return (
        <View style={styles.cardWrapper}>
            {empData?.managerId ? (
                <RNText style={styles.cardTitle}>{stringText.Manager}</RNText>
            ) : (
                <View style={{ width: '100%' }}>
                    <View
                        style={[
                            styles.titleSkeleton,
                            { padding: 12, width: 160 }
                        ]}
                    />
                </View>
            )}
            {empData?.managerId ? (
                <RNButton
                    ActiveOpacity={0.6}
                    handleOnPress={() => handelSetId(empData.managerId)}
                    style={{
                        flexDirection: 'row',
                        width: '100%',
                        alignItems: 'center',
                        marginVertical: 6,
                        gap: 20
                    }}
                >
                    <RNImage
                        source={{ uri: empData?.managerImageURL }}
                        style={styles.manegerProfileWrapper}
                    />
                    <View style={{ gap: 4, width:'85%' }}>
                        <RNText style={styles.smallTitle}>
                            {empData?.managerName}
                        </RNText>
                        {/* <RNText style={styles.smallSubTitle}>
                            {empData?.designation}
                        </RNText> */}
                    </View>
                </RNButton>
            ) : (
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginVertical: 6,
                        gap: 20,
                        width:'100%'
                    }}
                >
                    <View style={styles.manegerProfileWrapper} />
                    <View style={{ gap: 4 }}>
                        <>
                            <View
                                style={[
                                    styles.titleSkeleton,
                                    { padding: 8, width: 120 }
                                ]}
                            ></View>
                            <View
                                style={[
                                    styles.titleSkeleton,
                                    { padding: 6, width: 60 }
                                ]}
                            ></View>
                        </>
                    </View>
                </View>
            )}
        </View>
    );
};

export default ManegerCard;
