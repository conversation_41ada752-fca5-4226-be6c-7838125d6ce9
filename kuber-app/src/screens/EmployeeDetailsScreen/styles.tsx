import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

export default StyleSheet.create({
    container: {
        padding: 16,
        gap: 20
    },
    headerContainer: {
        backgroundColor: color.WHITE,
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20
    },
    headerText: {
        fontSize: 18,
        color: color.BLACK
    },
    cardWrapper: {
        padding: 16,
        elevation: 2,
        backgroundColor: color.WHITE,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        gap: 14
    },
    profileWrapper: {
        width: 120,
        aspectRatio: 1 / 1,
        borderRadius: 200,
        elevation: 4,
        backgroundColor:color.BLACK+10

    },
    manegerProfileWrapper: {
        width: 40,
        aspectRatio: 1 / 1,
        borderRadius: 200,
        backgroundColor:color.BLACK+10
    },
    drsListWrapper: {
        width: '100%',
        gap: 12
    },
    placeHolderValueWrapper: {
        alignItems: 'center',
        width:'50%'
    },
    titleSkeleton:{
        padding:20,
        backgroundColor:color.BLACK+10,
        width:'60%',
        borderRadius:20
    },
    title: {
        fontSize: 24,
        fontWeight: '600',
        color: color.BLACK
    },
    subTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: color.BLACK
    },
    placeHolder: {
        fontSize: 14,
        fontWeight: '400',
        color: color.BLACK
    },
    PlaceValue: {
        fontSize: 16,
        fontWeight: '500',
        color: color.BLACK
    },
    contactContainer:{
        flexDirection:'row',
         gap:10,
         alignItems:"center" 
    },
    PlaceValueContact: {
        fontSize: 16,
        fontWeight: '500',
        color: color.DARK_BLUE,
        width:'90%',
    },
    cardTitle: {
        fontSize: 20,
        fontWeight: '500',
        color: color.BLACK,
        width: '100%'
    },
    cardTouch: {
        alignItems: 'center',
        gap: 10
    },
    backdropLoader: {
        width: '100%',
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
        backgroundColor: color.BLACK+80
    },
    smallTitle:{
        fontSize:16,
        fontWeight:'500',
        width:'95%',
    },
    smallSubTitle:{
        fontSize:12,
        fontWeight:'400',
        width:'95%'
    },
    mainInfoView: {
        flexWrap: 'wrap',
        gap: 5,
        width: '100%',
        alignContent:'center',
        alignItems:'center'
    },
    contactInfoView: {
        flexWrap: 'wrap',
        gap: 5,
        width: '100%',
        borderTopWidth: 1,
        borderColor: color.GREAYSCALE,
        paddingTop: 10,
        paddingLeft:22
    },
    basicInfoView: {
        gap: 20,
        width: '100%',
        justifyContent: 'center',
        borderTopWidth: 1,
        borderTopColor: color.GREAYSCALE,
        paddingVertical: 10
    } 
});
