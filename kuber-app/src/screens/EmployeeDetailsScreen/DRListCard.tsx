import React from 'react';
import { View, FlatList } from 'react-native';
import RNText from '../../component/RNText';
import styles from './styles';
import RNImage from '../../component/RNImage';
import RNButton from '../../component/RNButton';

const DRListCard: React.FC<{ empData: any; handelSetId: any }> = ({
    empData,
    handelSetId
}) => {
    const renderItem = ({ item }: { item: any }) => (
        <RNButton
            key={item?.userId}
            ActiveOpacity={0.6}
            handleOnPress={() => handelSetId(item?.userId)}
            style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginVertical: 12,
                gap: 20
            }}
        >
            <RNImage
                source={{ uri: item?.imagePath }}
                style={styles.manegerProfileWrapper}
            />
            <View style={{ gap: 4, width:'80%'}}>
                <RNText style={styles.smallTitle}>{item?.employeeName}</RNText>
                <RNText style={styles.smallSubTitle}>{item?.designation}</RNText>
            </View>
        </RNButton>
    );

    const renderSkeleton = () => (
        <View
            style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginVertical: 6,
                gap: 20
            }}
        >
            <View style={styles.manegerProfileWrapper} />
            <View style={{ gap: 4 }}>
                <>
                    <View style={[styles.titleSkeleton, { padding: 8, width: 120 }]} />
                    <View style={[styles.titleSkeleton, { padding: 6, width: 60 }]} />
                </>
            </View>
        </View>
    );
    
    return (
        <View style={styles.cardWrapper}>
            {empData?.id ? (
                <RNText style={styles.cardTitle}>
                    {empData?.first_name}'s DRs{' '}
                </RNText>
            ) : (
                <View style={{ width: '100%' }}>
                    <View
                        style={[
                            styles.titleSkeleton,
                            { padding: 12, width: 160 }
                        ]}
                    />
                </View>
            )}
            <View style={styles.drsListWrapper}>
                <FlatList
                    data={empData?.DRS || Array.from({ length: 3 })}
                    renderItem={empData?.DRS ? renderItem : renderSkeleton}
                    keyExtractor={(item, index) => index.toString()}
                />
            </View>
        </View>
    );
};

export default DRListCard;
