import React from 'react';
import { Linking, ScrollView, View } from 'react-native';
import RNText from '../../component/RNText';
import styles from './styles';
import RNImage from '../../component/RNImage';
import ImageViewer from '../../component/ImageViewer';
import FontAwesome from "react-native-vector-icons/FontAwesome"
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons"
import { color } from '../../utils/constants/color';
import { stringText } from '../../utils/constants/stringsText';

const ProfileCard: React.FC<{ empData: any }> = ({ empData }) => {

    return (
        <View style={styles.cardWrapper}>
            {empData?.image_path ? (
                <ImageViewer imageLink={empData?.image_path} style={styles.profileWrapper} />
            ) : (
                <View style={[styles.profileWrapper]}></View>
            )}
            <View
                style={styles.mainInfoView}
            >
                {empData?.id ? (
                    <RNText style={styles.title}>
                        {empData?.first_name + ' ' + empData?.last_name}
                    </RNText>
                ) : (
                    <View
                        style={[
                            styles.titleSkeleton,
                            { padding: 14, width: '60%' }
                        ]}
                    ></View>
                )}
                {empData?.id ? (
                    <RNText style={styles.subTitle}>
                        {empData?.designation} 
                    </RNText>
                ) : (
                    <View
                        style={[
                            styles.titleSkeleton,
                            { padding: 12, width: '40%' }
                        ]}
                    ></View>
                )}
            </View>
            <View
                style={styles.contactInfoView}
            >
                <View >
                    {empData?.id ? (
                        <View style={styles.contactContainer}>
                            <MaterialCommunityIcons
                                name="email"
                                size={16}
                                color={color.BLACK}
                            />
                            <RNText style={styles.PlaceValueContact} onPress={() => Linking.openURL(`mailto:${empData?.username}`)}>
                                {empData?.username}
                            </RNText>
                        </View>
                    ) : (
                        <View
                            style={[
                                styles.titleSkeleton,
                                { padding: 8, width: 120, marginTop: 8 }
                            ]}
                        ></View>
                    )}
                </View>
                <View >
                    {empData?.id ? (
                        <View style={styles.contactContainer}>
                            <FontAwesome
                                name="phone"
                                size={16}
                                color={color.BLACK}
                            />
                            <RNText style={styles.PlaceValueContact} onPress={() => { Linking.openURL(`tel:${empData?.mobile_no}`); }}>
                                {empData?.mobile_no}
                            </RNText>
                        </View>
                    ) : (
                        <View
                            style={[
                                styles.titleSkeleton,
                                { padding: 8, width: 120, marginTop: 8 }
                            ]}
                        ></View>
                    )}
                </View>
            </View>
            <View
                style={styles.basicInfoView}
            >
                <View style={{ flexDirection: 'row' }}>
                    <View style={styles.placeHolderValueWrapper}>
                        {empData?.id ? (
                            <>
                                <RNText style={styles.placeHolder}>
                                    {stringText.EmployeeID}
                                </RNText>
                                <RNText style={styles.PlaceValue}>
                                    {empData?.employee_id}
                                </RNText>
                            </>
                        ) : (
                            <View
                                style={[
                                    styles.titleSkeleton,
                                    { padding: 12, width: '40%' }
                                ]}
                            ></View>
                        )}
                    </View>
                    <View style={styles.placeHolderValueWrapper}>
                        {empData?.officeTime ? (
                            <>
                                <RNText style={styles.placeHolder}>
                                    {' '} {stringText.OfficeTiming} {' '}
                                </RNText>
                                <RNText style={styles.PlaceValue}>
                                    {empData?.officeTime}
                                </RNText>
                            </>
                        ) : (
                            <>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        { padding: 6, width: 100, marginTop: 4 }
                                    ]}
                                ></View>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        { padding: 8, width: 80, marginTop: 4 }
                                    ]}
                                ></View>
                            </>
                        )}
                    </View>
                </View>
                <View style={{ flexDirection: 'row' }}>
                    <View style={styles.placeHolderValueWrapper}>
                        {empData?.id ? (
                            <>
                                <RNText style={styles.placeHolder}>
                                    {stringText.OfficeLocation}
                                </RNText>
                                <RNText style={styles.PlaceValue}>
                                    {empData?.companyLocationName === null ? '-' : empData?.companyLocationName}
                                </RNText>
                            </>
                        ) : (
                            <>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        { padding: 6, width: 100, marginTop: 4 }
                                    ]}
                                ></View>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        { padding: 8, width: 80, marginTop: 4 }
                                    ]}
                                ></View>
                            </>
                        )}
                    </View>
                    <View style={styles.placeHolderValueWrapper}>
                        {empData?.id ? (
                            <>
                                <RNText style={styles.placeHolder}>
                                    {' '}{stringText.WorkStation}{' '}
                                </RNText>
                                <RNText style={styles.PlaceValue}>
                                    {empData?.workStation === null || empData?.workStation == undefined ? '-' : empData.workStation}
                                </RNText>
                            </>
                        ) : (
                            <>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        { padding: 6, width: 100, marginTop: 4 }
                                    ]}
                                ></View>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        { padding: 8, width: 80, marginTop: 4 }
                                    ]}
                                ></View>
                            </>
                        )}
                    </View>
                </View>


            </View>
        </View>
    );
};

export default ProfileCard;
