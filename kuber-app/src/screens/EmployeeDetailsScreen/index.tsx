import React, { useEffect, useRef, useState } from 'react';
import { SafeAreaView, ScrollView, View } from 'react-native';
import RNText from '../../component/RNText';
import { color } from '../../utils/constants/color';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import Entypo from 'react-native-vector-icons/Entypo';
import styles from './styles';
import ProfileCard from './ProfileCard';
import DRListCard from './DRListCard';
import ManegerCard from './ManegerCard';
import RNActivityIndicator from '../../component/Loader';
import { useFocusEffect } from '@react-navigation/native';
import { httpGet } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';
import { log } from 'react-native-reanimated';
import { successToast } from '../../component/SuccessToast';
import RNButton from '../../component/RNButton';
import apiConstant from '../../utils/constants/apiConstant';

type EmployeeDetailsScreenProps = {
    navigation: any;
    route: any;
};

const EmployeeDetailsScreen: React.FC<EmployeeDetailsScreenProps> = ({
    navigation,
    route
}) => {
    const scrollViewRef = useRef(null);
    const [empData, setEmpData] = useState<any>(null);
    const [empId, setEmpId] = useState(route.params.empId);
    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setFetching(true);
        httpGet(`${apiConstant.BASIC_INFO}?userId=${empId}&isGlobal=true`)
            .then((response: any) => {
                const data = JSON.parse(response)?.data;
                if (data) {
                    setEmpData(data)
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
                successToast(err?.response?.data?.message);
            });
    }, [empId]);

    const handelSetId = (id: any) => {
        setEmpId(id);
    };

    useFocusEffect(() => {
        scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
    });    

    return (
        <SafeAreaView style={{ flex: 1 }}>

            <View
                style={{
                    backgroundColor: color.WHITE,
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 16
                }}
            >
                <RNButton
                    handleOnPress={() => navigation.goBack()}
                    style={{ marginVertical: 10, marginRight: 20 }}
                >
                    <Entypo
                        name="chevron-left"
                        color={color.DARK_BLUE}
                        size={24}
                    />
                </RNButton>
                <View style={{ flexDirection: 'row' }}>
                    <RNText style={styles.headerText}>
                        {navigationStringText.EmployeeDetails}
                    </RNText>
                </View>
            </View>
            <ScrollView
                ref={scrollViewRef}
                contentContainerStyle={{ flexGrow: 1 }}
            >
                <View style={styles.container}>
                    <ProfileCard empData={empData} />
                    <ManegerCard
                        handelSetId={handelSetId}
                        navigation={navigation}
                        empData={empData}
                    />
                    {empData?.DRS?.length > 0 &&
                        <DRListCard handelSetId={handelSetId} empData={empData} />
                    }
                </View>
            </ScrollView>
            {fetching && <View style={styles.backdropLoader}></View>}
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};

export default EmployeeDetailsScreen;
