import React from 'react';
import { View, } from 'react-native';
import { DataTable } from 'react-native-paper';
import styles from './styles';

export type Props = {
    itemData: any,
    isDinnerCard?: boolean
}

const LunchDinnerCard = (props: Props) => {

    const { itemData, isDinnerCard } = props;

    return (
        <View>
            <DataTable>
                <DataTable.Row style={styles.rowStyle}>
                    <DataTable.Cell textStyle={
                        isDinnerCard ? styles.TextDataDinner : styles.TextDataLunch}>
                        {itemData}
                    </DataTable.Cell>
                </DataTable.Row>
            </DataTable>
        </View>
    );
}

export default LunchDinnerCard;
