import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    tableView: {
        margin: 20,
        backgroundColor: color.WHITE,
        borderWidth: 1,
        borderColor: color.ACCENT_BLUE,
        borderRadius: 5,
        overflow: 'hidden'
    },
    headerView: {
        backgroundColor: color.ACCENT_BLUE,
        borderTopRightRadius: 4,
        borderTopLeftRadius: 4,
        borderBottomWidth: 1,
        borderBottomColor: color.ACCENT_BLUE
    },
    headerLunch: {
        fontSize: 16,
        fontFamily: 'Poppins-SemiBold',
        color: color.WHITE,
        marginLeft: 5
    },
    headerDinner: {
        fontSize: 16,
        color: color.WHITE,
        marginLeft: 5,
        fontFamily: 'Poppins-SemiBold',
    },
    headerDinnerView: {
        backgroundColor: color.ACCENT_BLUE,
        borderTopRightRadius: 4,
        borderTopLeftRadius: 4,
        borderBottomWidth: 1,
        borderBottomColor: color.ACCENT_BLUE
    },
    ButtonView: {
        width: 60,
        position: 'absolute',
        right: 20,
        bottom: 20
    },
    ImgButton: {
        height: 56,
        width: 56,
        resizeMode: 'cover',
        justifyContent: 'center',
        alignItems: 'center'
    },
    TextButton: {
        color: color.WHITE,
        fontSize: 15,
        textAlign: 'center',
        fontFamily: 'Poppins-Regular',
        height: 60,
        width: 60,
        marginTop: 10
    },
    imgHistory: {
        height: 20,
        width: 20,
        borderWidth: 1,
        borderColor: color.PRIMARY_ACTIVE
    },
    rowStyle: {
        borderBottomWidth: -2,
        margin: -7,
        marginLeft: 5
    },
    itemStyles: {
        fontFamily: 'Poppins-Regular',
        fontSize: 14,
        color: color.GREY_COLOR
    },
    plusImage: {
        height: 20,
        width: 36,
        resizeMode: 'contain'
    },
    FoodNotAvailable: {
        height: 137,
        width: 123
    },
    TextDataLunch: {
        fontFamily: 'Poppins-Regular',
        fontSize: 14,
        textTransform: 'capitalize',
        color: color.WHITE
    },
    TextDataDinner: {
        fontFamily: 'Poppins-Regular',
        fontSize: 14,
        textTransform: 'capitalize',
        color: color.WHITE
    },
    mainViewstyles: {
        backgroundColor: color.WHITE,
        flexDirection: 'row',
        paddingVertical: 21
    },
    LeftIcon: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    FullDayTextView: {
        flex: 4,
        alignItems: 'center',
        justifyContent: 'center'
    },
    FullDayText: {
        color: color.GREY_COLOR,
        fontSize: 18,
        fontFamily: 'Poppins-Regular',
    },
    FullMonthTextView: {
        flexDirection: 'row',
        marginTop: 3
    },
    FullMonthText: {
        color: color.GREAYSCALE,
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
    },
    DateArraySpilt: {
        color: color.GREAYSCALE,
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
    },
    ArraySplitDate: {
        color: color.GREAYSCALE,
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
    },
    RightIcon: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    FoodNotAvailableText: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    },
    mainSafeAreaView:{
        flex: 1
    },
    mainScrollView:{ 
        flexGrow: 1 
    }
});

export default styles;