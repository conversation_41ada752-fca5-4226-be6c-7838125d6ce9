import { StyleSheet } from "react-native";
import { color } from "../../utils/constants/color";

const styles = StyleSheet.create({
    InButton: {
        borderWidth: 1,
        width: 160,
        height: 48,
        borderRadius: 10,
        borderColor: color.DARK_BLUE
    },
    InButtonText: {
        textAlign: 'center',
        fontSize: 15,
        padding: 10,
        fontFamily:'Poppins-Regular',
    },
    buttonView: {
        justifyContent: 'space-evenly',
        flexDirection: 'row'
    },
    mainView: {
        flex:1
    },
    OutButton: {
        borderWidth: 1,
        width: 160,
        height: 48,
        borderRadius: 10,
        borderColor: color.DARK_BLUE
    },
    OutButtonText: {
        textAlign: 'center',
        fontSize: 15,
        padding: 10,
        fontFamily:'Poppins-Regular',
    },
    textHead: {
        color: color.GREY_COLOR,
        fontSize: 15,
        margin: 16,
        fontFamily:'Poppins-Regular',
    },
    timerText: {
        color: color.GREY_COLOR,
        fontSize: 15,
        margin: 16,
        fontFamily:'Poppins-Regular',
        alignSelf: 'center',
    },
    qrImg: {
        alignSelf: 'center',
        height: 255,
        width: 255
    },
    ImgView: {
        borderWidth: 2,
        borderColor: color.DARK_BLUE,
        width: 355,
        height: 355,
        alignSelf: 'center',
        borderRadius: 4,
        backgroundColor: color.WHITE,
        marginTop: 10,
        justifyContent: 'center',
        alignItems: 'center'
    }
})

export default styles;