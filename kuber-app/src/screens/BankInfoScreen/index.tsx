import React, { useState, useEffect } from 'react';
import { httpGet } from '../../utils/http';
import { SafeAreaView, ScrollView, Text, View } from 'react-native';
import RNActivityIndicator from '../../component/Loader';
import { stringText } from '../../utils/constants/stringsText';
import BankInfoView from './BankInfoView';
import RNText from '../../component/RNText';
import apiConstant from '../../utils/constants/apiConstant';

export type Props = {
    navigation: any;
};

export type BankInfoType = {
    bank_name: string | any;
    id_bank: string;
    branch_name: string;
    bank_branch_address_no1: string;
    bank_branch_address_no2: string;
    accountNumber: string;
    city: string;
    state: string;
    country: number;
    accountType: number;
    stateDropdown: any;
    countryDropdown: any;
    accountTypeDropdown: any;
};

const BankInfoScreen = (props: Props) => {
    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [bankInfo, setBankInfo] = useState<String>('');
    const [stateDropDown, setStateDropDown] = useState<[]>([]);
    const [countryDropDown, setCountryDropDown] = useState<[]>([]);
    const [accountTypeDropDown, setAccountTypeDropDown] = useState<[]>([]);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setFetching(true);
        httpGet(apiConstant.BANK_INFO)
            .then((response: any) => {
                const employeeBankInfoData = JSON.parse(response)?.data;                
                if (employeeBankInfoData) {
                    setBankInfo(employeeBankInfoData);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
        httpGet(apiConstant.BANK_INFO_COUNTRIES)
            .then((response: any) => {
                const countries = JSON.parse(response)?.data;
                if (countries) {
                    setCountryDropDown(countries);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });

        httpGet(apiConstant.BANK_INFO_STATE)
            .then((response: any) => {
                const states = JSON.parse(response)?.data;
                if (states) {
                    setStateDropDown(states);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });

        httpGet(apiConstant.BANK_INFO_ACCOUNT_TYPE)
            .then((response: any) => {
                const accountType = JSON.parse(response)?.data;
                if (accountType) {
                    setAccountTypeDropDown(accountType);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }, []);
    return (
        <SafeAreaView style={{ flex: 1 }}>
            <ScrollView>
                {!fetching && 
                <BankInfoView
                    bank_name={bankInfo?.bank_name}
                    id_bank={bankInfo?.id_bank}
                    branch_name={bankInfo?.branch_name}
                    accountNumber={bankInfo?.account_no}
                    bank_branch_address_no1={bankInfo?.bank_branch_address_no1}
                    bank_branch_address_no2={bankInfo?.bank_branch_address_no2}
                    city={bankInfo?.city}
                    state={bankInfo?.id_state}
                    country={bankInfo?.id_country}
                    accountType={bankInfo?.account_type}
                    stateDropdown={stateDropDown}
                    countryDropdown={countryDropDown}
                    accountTypeDropdown={accountTypeDropDown}
                />
                }
            </ScrollView>
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};

export default BankInfoScreen;