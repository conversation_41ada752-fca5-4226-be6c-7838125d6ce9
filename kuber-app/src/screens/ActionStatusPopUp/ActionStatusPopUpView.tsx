import React, { FC } from 'react';
import { Modal, View, Text } from 'react-native';
import { Card, Icon } from '@rneui/themed';
import { stringText } from '../../utils/constants/stringsText';
import styles from '../../component/Styles';
import RNText from '../../component/RNText';
import { color } from '../../utils/constants/color';
import RNButton from '../../component/RNButton';

export type Props = {
    visible?: boolean;
    navigation: any;
    setActionPopupVisibility: (visible: boolean) => void;
    employeeName: string;
    message: string;
    userData:any;
    postLeaveStatus: (leaveId: number, leaveStatus: number) => void;
};

const ActionStatusPopUpView: FC<Props> = (props) => {
    const {
        visible,
        navigation,
        setActionPopupVisibility,
        employeeName,
        message,
        postLeaveStatus,
        userData
    } = props;

    return (
        <Modal transparent={visible} visible={visible} animationType="fade">
            <View
                style={{
                    width: '100%',
                    height: '100%',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: color.BLACK + 90
                }}
            >
                <View style={styles.LogOutPopup}>
                    <Card
                        wrapperStyle={{ alignItems: 'center' }}
                        containerStyle={[
                            styles.LogOutPopUpCardView,
                            {
                                elevation: 4,
                                padding: 0,
                                borderRadius: 4,
                                margin: 0
                            }
                        ]}
                    >
                        <RNText style={styles.LogOutTextPopup}>
                            {message}
                            {employeeName} {stringText.Leave}
                        </RNText>
                        <View style={styles.LogOutPopUpButtonView}>
                            <RNButton
                                handleOnPress={() =>
                                    setActionPopupVisibility(!visible)
                                }
                                style={styles.NoTextView}
                            >
                                <RNText style={styles.NoTextStyles}>
                                    {stringText.Cancel}
                                </RNText>
                            </RNButton>
                            <RNButton
                                handleOnPress={() => {
                                    setActionPopupVisibility(!visible);
                                    postLeaveStatus(userData.id,userData.status==1?2:1)
                                }}
                                style={styles.LogOutPopUpYesView}
                            >
                                <RNText style={styles.LogOutTextStyles}>
                                    {stringText.Ok}
                                </RNText>
                            </RNButton>
                        </View>
                    </Card>
                </View>
            </View>
        </Modal>
    );
};

export default ActionStatusPopUpView;
