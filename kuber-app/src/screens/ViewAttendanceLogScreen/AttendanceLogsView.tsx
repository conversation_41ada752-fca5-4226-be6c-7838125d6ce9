import React from 'react';
import {
    SafeAreaView,
    ScrollView,
    View
} from 'react-native';
import Spinner from '../../component/Loader';
import { Table, Row, Rows } from 'react-native-table-component';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import styles from './styles';
import { stringText } from '../../utils/constants/stringsText';
import RNText from '../../component/RNText';

export type Props = {
    tableData: any,
    fetching: boolean,
    errorHandlerVisibility: boolean,
    errorHandlerMessage: string,
    errorHandlerClicked: (errorHandlerVisibility: boolean, errorHandlerMessage: string) => void
}

const AttendanceLogsView = (props: Props) => {

    const { tableData, fetching, errorHandlerVisibility, errorHandlerMessage, errorHandlerClicked } = props;

    return (
        <SafeAreaView style={styles.mainView}>
            <ScrollView>
                <RNText style={styles.textOrder}>{stringText.AttendanceHistory}</RNText>
                {tableData?.tableData?.length > 0 ? (
                    <View style={styles.TableView}>
                        <Table
                            borderStyle={styles.TableBorder}
                        >
                            <Row
                                data={tableData.tableHead}
                                style={styles.head}
                                textStyle={styles.headText}
                            />
                            <Rows
                                data={tableData.tableData}
                                textStyle={styles.text}
                            />
                        </Table>
                    </View>
                ) : (
                    !fetching && (
                        <View
                            style={styles.DataNotAvailableTextView}
                        >
                            <RNText
                                style={styles.DataAvaibleText}
                            >
                                {stringText.DataNotAvailable}
                            </RNText>
                        </View>
                    )
                )}
                <ErrorHandlerPopup
                    visible={errorHandlerVisibility}
                    errorHandlerMessage={errorHandlerMessage}
                    errorHandlerClicked={errorHandlerClicked}
                />
            </ScrollView>
            <Spinner animating={fetching} />
        </SafeAreaView>
    )
}

export default AttendanceLogsView;