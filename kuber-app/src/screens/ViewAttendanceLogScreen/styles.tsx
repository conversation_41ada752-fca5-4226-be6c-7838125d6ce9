import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    mainView: {
        backgroundColor: color.OFF_WHITE,
        flex: 1
    },
    containerView: {
        flexDirection: 'row'
    },
    calenderView: {
        flex: 1,
        alignSelf: 'center',
        justifyContent: 'center',
        padding: 10,
        height: 18,
        width: 20,
        resizeMode: 'contain'
    },
    inputText: {
        borderWidth: 1,
        borderColor: color.DARK_BLUE,
        width: 99,
        height: 39,
        borderRadius: 5,
        backgroundColor: color.WHITE,
        fontSize: 12,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-Regular'
    },
    TextImg: {
        flexDirection: 'row',
        justifyContent: 'space-around'
    },
    ButtonImg: {
        flexDirection: 'row',
        justifyContent: 'space-around'
    },
    TextImg1: {
        flexDirection: 'row',
        justifyContent: 'space-around'
    },
    textOrder: {
        margin: 16,
        color: color.GREY_COLOR,
        fontSize: 14,
        fontFamily: 'Poppins-SemiBold'
    },
    tableView: {
        margin: 20,
        backgroundColor: color.WHITE,
        borderWidth: 1
    },
    ImgView: {
        padding: 10,
        borderWidth: 1,
        borderColor: color.DARK_BLUE,
        marginLeft: 5,
        height: 39,
        width: 39,
        borderRadius: 5
    },
    textK: {
        color: color.GREY_COLOR,
        fontSize: 14,
        fontFamily: 'Poppins-Regular'
    },
    textHead: {
        fontFamily: 'Poppins-Regular',
        fontSize: 12,
        color: color.GREY_COLOR,
        textAlign: 'center'
    },
    rowStyle: {
        borderBottomWidth: 1,
        borderColor: color.PRIMARY_ACTIVE
    },
    searchButton: {
        display: 'flex',
        marginBottom: 10,
        borderWidth: 2,
        width: 200,
        height: 50,
        backgroundColor: color.DARK_BLUE,
        alignSelf: 'center',
        borderColor: color.WHITE,
        borderRadius: 15,
        justifyContent: 'center'
    },
    searchText: {
        color: color.WHITE,
        alignContent: 'center',
        alignSelf: 'center'
    },
    headerView: {
        backgroundColor: color.WHITE,
        borderBottomWidth: 1,
        borderBottomColor: 'black'
    },
    headerLunch: {
        fontFamily: 'Poppins-Regular',
        fontSize: 12,
        color: color.GREY_COLOR,
        textAlign: 'center'
    },
    head: {
        height: 44,
        backgroundColor: color.WHITE
    },
    headText: {
        fontSize: 14,
        color: color.GREY_COLOR,
        textAlign: 'center',
        fontFamily: 'Poppins-Bold'
    },
    text: {
        fontSize: 12,
        color: color.GREY_COLOR,
        textAlign: 'center',
        fontFamily: 'Poppins-Regular',
        margin: 12
    },
    TableBorder: {
        borderWidth: 1,
        borderColor: color.DARK_BLUE
    },
    TableView: {
        marginTop: 20,
        marginHorizontal: 20,
        marginBottom: 20
    },
    DataNotAvailableTextView: {
        flex: 1,
        marginHorizontal: 16,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 20
    },
    DataAvaibleText: {
        color: color.BLACK,
        fontSize: 14,
        fontFamily: 'Poppins-Regular'
    },
    viewLogsView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    }
});

export default styles;