import React, { useEffect, useState } from 'react';
import { stringText } from '../../utils/constants/stringsText';
import { httpGet } from '../../utils/http';
import AttendanceLogsView from './AttendanceLogsView';
import { BackHandler } from 'react-native';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import apiConstant from '../../utils/constants/apiConstant';

const ViewAttendanceLogsScreen = (props: any) => {

    const { route } = props;
    const { navigation } = props;

    const { userId } = route.params
    const [id, setId] = useState(route?.params && route?.params?.userId ? route?.params?.userId : null)
    const [fetching, setFetching] = useState<boolean>(false);
    const [tableData, setTableData] = useState<any>();
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
    const [idUser, setIdUser] = useState();

    useEffect(() => {
        getLogs()
    }, []);

    useEffect(() => {
        const backAction = () => {
            navigation.navigate(navigationStringText.AttendanceHistory);
            return true; // Prevent default behavior (exit the app)
        };

        const backHandler = BackHandler.addEventListener(
            'hardwareBackPress',
            backAction
        );

        return () => backHandler.remove();
    }, [navigation]);
    const getLogs = () => {
        setFetching(true);
        httpGet(`${apiConstant.ATTENDANCE_LOGS}?attendance_id=${id}`)
            .then((res: any) => {
                const tableColumn: Array<any> = [];
                JSON.parse(res)?.data?.map(
                    (item: any, index: number) => {
                        const date = item?.date
                        const inTime = item?.in_time
                        const outTime = item?.out_time
                        const totalHrs = item?.total_hours
                        setIdUser(item?.id)
                        const innerItem = [
                            date,
                            inTime,
                            outTime,
                            totalHrs
                        ];
                        tableColumn?.push(innerItem);
                    }
                );
                const tableData = {
                    tableHead: [`${stringText.DateText}`, `${stringText.InTime}`, `${stringText.OutTime}`, `${stringText.TotalHrs}`],
                    tableData: tableColumn
                };
                setTableData(tableData);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const errorHandlerClicked = (errorHandlerVisibility: boolean, errorHandlerMessage: string) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    return (
        <AttendanceLogsView
            tableData={tableData}
            fetching={fetching}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
        />
    );
};

export default ViewAttendanceLogsScreen;
