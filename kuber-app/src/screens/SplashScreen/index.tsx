import AsyncStorage from '@react-native-async-storage/async-storage';
import { StackActions } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import SplashScreen from 'react-native-splash-screen';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import VersionCheck from 'react-native-version-check';
import { Platform } from 'react-native';
import ForceUpdatePopup from '../../component/ForceUpdatePopup';

const SplashScreenView = (props: any) => {
    const { navigation } = props;
    const [updated, setUpdated] = useState<boolean>(false);

    const checkAppVersion = async () => {
        try {
            const latestVersion =
                Platform.OS === 'ios'
                    ? await fetch(
                        `https://itunes.apple.com/in/lookup?bundleId=com.tudip.kuberproject&date=${Date.now()}`
                    )
                        .then((r) => r.json())
                        .then((res) => {
                            return res?.results[0]?.version;
                        })
                    : await VersionCheck.getLatestVersion({
                        provider: 'playStore',
                        packageName: 'com.kuberproject',
                        ignoreErrors: true
                    });

            const currentVersion = VersionCheck.getCurrentVersion();
            AsyncStorage.setItem("user_version", currentVersion);
            AsyncStorage.setItem("playstore_version", latestVersion);
            if (latestVersion != currentVersion && latestVersion!=null && currentVersion!=null) {
                setUpdated(false);
            } else {
                setUpdated(false);
                checkAuthToken();
            }
        } catch (error) {
            console.error('Error checking app version:', error);
        }
    };

    useEffect(() => {
        checkAppVersion();
    }, []);

    const checkAuthToken = async () => {
        const usertoken = await AsyncStorage.getItem('AuthToken');
        if (!updated) {
            if (usertoken !== null) {
                navigation.dispatch(
                    StackActions.replace(navigationStringText.NavigationDrawer)
                );
            } else {
                navigation.dispatch(
                    StackActions.replace(navigationStringText.Login)
                );
            }
        }
        SplashScreen.hide();
    };

    return updated ? <ForceUpdatePopup /> : null;
};

export default SplashScreenView;
