import React from 'react';
import { <PERSON>, FlatList, ScrollView } from 'react-native';
import { DataTable } from 'react-native-paper';
import RNText from '../../component/RNText';
import styles from './styles';
import { stringText } from '../../utils/constants/stringsText';

interface Props {
    lateAttendance: {
        lateAttendanceDetails: any[];
        noAttendanceDetails: any[];
        officeTiming: string;
    };
}

const LateAttendanceTable: React.FC<Props> = ({ lateAttendance }) => {

    const renderLateAttendance = ({ item }: { item: any }) => {
        function extractDateTime(dateTimeString: any) {
            const dateObj = new Date(dateTimeString);
            dateObj.setHours(dateObj.getHours() - 5);
            dateObj.setMinutes(dateObj.getMinutes() - 30);
            let hours = dateObj.getHours();
            const minutes = String(dateObj.getMinutes()).padStart(2, "0");
            const timeOfDay = hours >= 12 ? "PM" : "AM";
            hours = hours % 12 || 12;
            const formattedHours = String(hours).padStart(2, "0");
            const time = `${formattedHours}:${minutes} ${timeOfDay}`;
            return time;
        }

        const getDayOfWeek = (dateString: any) => {
            const parts = dateString.split("-");
            const day = parseInt(parts[0], 10);
            const month = parseInt(parts[1], 10) - 1;
            const year = parseInt(parts[2], 10);
            const daysOfWeek = [
                "Sunday",
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
            ];
            const dateObj = new Date(year, month, day);
            const dayOfWeek = daysOfWeek[dateObj.getDay()];
            return dayOfWeek;
        };

        return (
            <DataTable.Row style={styles.tableValueTextWrapper}>
                <RNText style={styles.tableValueText}>{item.date}</RNText>
                <RNText style={styles.tableValueText}>{item.late_by}</RNText>
                <RNText style={styles.tableValueText}>{extractDateTime(item.actual_scan_time)}</RNText>
                <RNText style={styles.tableValueText}>{extractDateTime(item.expected_scan_time)}</RNText>
                {/* <RNText style={styles.tableValueText}>{getDayOfWeek(item.date)}</RNText> */}
            </DataTable.Row>
        );
    };

    return (

        <View style={styles.mainContainer}>
            <RNText style={styles.tableTitle}>{stringText.LateAttendance}</RNText>
            <View style={styles.container}>
                {/* <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ flexGrow: 1 }}> */}
                    <View style={styles.lateAttandanceMainContainer}>
                        <View style={styles.tableTitleWrapper}>
                            <RNText style={styles.tableTitleText}>{stringText.DATE}</RNText>
                            <RNText style={styles.tableTitleText}>{stringText.LATEBY}</RNText>
                            <RNText style={styles.tableTitleText}>{stringText.ACTUALREPORTING}</RNText>
                            <RNText style={styles.tableTitleText}>{stringText.REPORTINGTIME}</RNText>
                            {/* <RNText style={[styles.tableTitleText,]}>DAY</RNText> */}
                        </View>
                        {
                            lateAttendance?.lateAttendanceDetails.length != 0 ?
                                <FlatList
                                    data={lateAttendance?.lateAttendanceDetails}
                                    renderItem={renderLateAttendance}
                                    keyExtractor={(item, index) => index.toString()}
                                />
                                :
                                <RNText style={styles.noDataText}>{stringText.DataNotAvailable}</RNText>
                        }
                    </View>
                {/* </ScrollView> */}
            </View>
        </View>
    );
};

export default LateAttendanceTable;
