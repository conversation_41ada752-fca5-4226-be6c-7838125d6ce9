import { Platform, StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    parentContainer: {
        flex: 1,
        margin: 10
    },
    mainContainer: {
        backgroundColor: color.WHITE,
        marginVertical: 10,
        borderRadius: 6
    },
    dropDown: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK,
        justifyContent: 'center',
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 6,
        height: 40,
        backgroundColor: color.WHITE
    },
    lateAttandanceMainContainer: {
        width: '100%'
    },
    container: {
        justifyContent: 'center',
        alignItems: 'center'
        // borderRadius:6,
    },
    tableValuesWrapper: {
        flexDirection: 'row',
        gap: 40,
        borderBottomWidth: 2,
        borderColor: color.BACKGROUND_COLOR,
        paddingHorizontal: 10
    },
    tableTitleWrapper: {
        backgroundColor: color.ACCENT_BLUE,
        flexDirection: 'row',
        width: '100%',
        padding: 10
        // borderTopRightRadius:6,
        // borderTopLeftRadius:6
    },
    tableTitleText: {
        color: color.WHITE,
        fontSize: 14,
        fontWeight: Platform.OS == 'ios' ? '500' : '900',
        fontFamily: 'Poppins-Regular',
        width: '25%',
        alignSelf: 'center',
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center'
    },
    tableValueTextWrapper: {
        paddingVertical: 8,
        backgroundColor: color.WHITE,
        borderBottomLeftRadius: 6,
        borderBottomRightRadius: 6
    },
    tableValueText: {
        color: color.GREY_COLOR,
        fontSize: 14,
        fontWeight: Platform.OS == 'ios' ? '500' : '800',
        fontFamily: 'Poppins-Regular',
        width: '25%',
        alignSelf: 'center',
        alignContent: 'center',
        textAlign: 'center'
    },
    reportTimeTitle: {
        textAlign: 'center',
        margin: 10,
        fontWeight: 'bold',
        paddingVertical: 0,
        fontSize: 16
    },
    tableTitle: {
        textAlign: 'center',
        margin: 10,
        fontWeight: 'bold',
        paddingVertical: 0,
        fontSize: 16,
        color: color.DARK_BLUE
    },
    noDataText: {
        paddingVertical: 10,
        fontWeight: 'bold',
        alignSelf: 'center'
    }
});
export default styles;
