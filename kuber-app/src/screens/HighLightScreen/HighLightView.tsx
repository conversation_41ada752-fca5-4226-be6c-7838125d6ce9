import React from 'react';
import { FlatList, ImageBackground, Platform, RefreshControl, SafeAreaView, ScrollView, View } from 'react-native';
import Spinner from '../../component/Loader';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import styles from './styles';
import GreetingCard from '../../component/GreetingCard';
import { stringText } from '../../utils/constants/stringsText';
import RNText from '../../component/RNText';
import imageConstant from '../../utils/constants/imageConstant';

export type Props = {
    navigation: any;
    refreshing: boolean;
    datamonth: Array<any>;
    datamonthbirth: Array<any>;
    fetching: boolean;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
    onRefresh: () => void,
    anniversaryColorFinder: Function,
    birthdayColorFinder: (index: number) => void

};

const HighLightView = (props: Props) => {
    let ListviewRef;

    const { navigation, refreshing, datamonth, datamonthbirth, fetching, errorHandlerVisibility,
        errorHandlerMessage, errorHandlerClicked, onRefresh, anniversaryColorFinder,
        birthdayColorFinder } = props;

    return (
        <>
            <SafeAreaView>
                <ScrollView
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={onRefresh}
                        />
                    }
                >
                    <View style={styles.mainViewStyles}>
                        <ImageBackground
                            resizeMode="stretch"
                            source={imageConstant.TopSecondBackground}
                            style={styles.ImageBackTop}
                        >
                            <ImageBackground
                                resizeMode="stretch"
                                source={imageConstant.TopBackground}
                                style={styles.SecondImageBackTop}
                            >
                                <View>
                                    <RNText style={styles.textStyle}>
                                        {stringText.DashboardTittle}
                                    </RNText>
                                    <RNText style={styles.textStyle}>
                                        {stringText.DashboardTittleBirthday}
                                    </RNText>
                                </View>
                            </ImageBackground>
                        </ImageBackground>
                    </View>
                    {datamonth?.length > 0 && (
                        <>
                            <RNText style={[styles.styleText, Platform.OS === 'ios' && { fontWeight: '500' }]}>
                                {stringText.AnniversaryGreeting}
                            </RNText>
                            <FlatList
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                renderItem={({ item, index }) =>
                                    <GreetingCard
                                        item={item}
                                        index={index}
                                        isBirthdayCard={false}
                                        anniversaryColorFinder={anniversaryColorFinder}
                                        navigation={navigation}
                                    />
                                }
                                data={datamonth}
                                ref={(ref) => {
                                    ListviewRef = ref;
                                }}
                                keyExtractor={(item: any) => item?.id}
                            />
                        </>
                    )}
                    {datamonthbirth?.length > 0 && (
                        <>
                            <RNText style={[styles.styleText, Platform.OS === 'ios' && { fontWeight: '500' }]}>
                                {stringText.BirthdayGreeting}
                            </RNText>
                            <FlatList
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                renderItem={({ item, index }) =>
                                    <GreetingCard
                                        item={item}
                                        index={index}
                                        birthdayColorFinder={birthdayColorFinder}
                                        isBirthdayCard={true}
                                        navigation={navigation}

                                    />
                                }
                                data={datamonthbirth}
                                ref={(ref) => {
                                    ListviewRef = ref;
                                }}
                                keyExtractor={(item: any) => item?.id}
                            />
                        </>
                    )}

                </ScrollView>
            </SafeAreaView>
            <Spinner animating={fetching} />
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </>
    );
};

export default HighLightView;
