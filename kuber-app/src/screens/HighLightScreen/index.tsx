import React, { useCallback, useEffect, useState } from 'react';
import { httpGet } from '../../utils/http';
import moment from 'moment';
import { color } from '../../utils/constants/color';
import { stringText } from '../../utils/constants/stringsText';
import { SafeAreaView } from 'react-native';
import apiConstant from '../../utils/constants/apiConstant';
import HighLightView from './HighLightView';

const HighLightScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
    const [datamonth, setDatamonth] = useState<Array<any>>([]);
    const [datamonthbirth, setDatamonthbirth] = useState<Array<any>>([]);
    const [fetching, setFetching] = useState<boolean>(false);
    const [startmonthdata, setStartonthdata] = useState(new Date());
    const [lastmonthdata, setLastmonthdata] = useState(new Date());
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [refreshing, setRefreshing] = useState<boolean>(false);
    const [expandedCards, setExpandedCards] = useState(
        Array(datamonthbirth.length).fill(false)
    );

    useEffect(() => {
        getAnniversary();
        getBirthDate();
    }, []);

    const getBirthDate = () => {
        setFetching(true);
        httpGet(
            `${apiConstant.TUDIP_BIRTHDAY_ANNIVERSARY}?fetch_type=birthday&from_date=${moment(
                startmonthdata
            ).format('YYYY-MM-01')}&to_date=${moment(lastmonthdata).format(
                'YYYY-MM-' + moment().daysInMonth()
            )}&orderby=name&is_active=1&happy=0`
        )
            .then((response: any) => {
                setDatamonthbirth(JSON.parse(response)?.data);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(
                    true,
                    err?.response?.data?.message ||
                    stringText.SomethingWentwrong
                );
            });
    };

    const getAnniversary = () => {
        setFetching(true);
        httpGet(
            `${apiConstant.TUDIP_BIRTHDAY_ANNIVERSARY}?fetch_type=anniversary&from_date=${moment(
                startmonthdata
            ).format('YYYY-MM-01')}&to_date=${moment(lastmonthdata).format(
                'YYYY-MM-' + moment().daysInMonth()
            )}&orderby=name&is_active=1&happy=0`
        )
            .then((response: any) => {
                setDatamonth(JSON.parse(response)?.data);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(
                    true,
                    err?.response?.data?.message ||
                    stringText.SomethingWentwrong
                );
            });
    };



    const onRefresh = useCallback(() => {
        setRefreshing(true);
        setTimeout(() => {
            setRefreshing(false);
            getAnniversary();
            getBirthDate();
        }, 2000);
    }, []);

    const anniversaryColorFinder = (index: number) => {
        let returnType = '';
        if ((index + 1) % 4 === 1) {
            returnType = color.LIGHT_BLUE;
        } else if ((index + 1) % 4 === 2) {
            returnType = color.LIGHT_RED;
        } else if ((index + 1) % 4 === 3) {
            returnType = color.LIGHT_YELLO;
        } else {
            returnType = color.LIGHT_GREEN;
        }
        return returnType;
    };

    const birthdayColorFinder = (index: number) => {
        let returnType = '';
        if ((index + 1) % 4 === 1) {
            returnType = color.LIGHT_GREEN;
        } else if ((index + 1) % 4 === 2) {
            returnType = color.LIGHT_YELLO;
        } else if ((index + 1) % 4 === 3) {
            returnType = color.LIGHT_RED;
        } else {
            returnType = color.LIGHT_BLUE;
        }
        return returnType;
    };

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <HighLightView
                refreshing={refreshing}
                datamonth={datamonth}
                datamonthbirth={datamonthbirth}
                fetching={fetching}
                errorHandlerVisibility={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
                onRefresh={onRefresh}
                anniversaryColorFinder={anniversaryColorFinder}
                birthdayColorFinder={birthdayColorFinder}
                navigation={navigation}
            />
        </SafeAreaView>
    );
};

export default HighLightScreen;
