import React, { useEffect, useState } from 'react';
import { SafeAreaView, FlatList, View } from 'react-native'; // Updated import
import EmpListCard from './EmployeeListCard';
import { httpGet } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';
import Ionicons from 'react-native-vector-icons/Ionicons';
import styles from './styles';
import { color } from '../../utils/constants/color';
import RNText from '../../component/RNText';
import RNActivityIndicator from '../../component/Loader';
import { useNavigation } from '@react-navigation/native';
import EmployeeDetailsScreen from '../EmployeeDetailsScreen';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import SearchBar from '../../component/SearchBar';
import RNButton from '../../component/RNButton';
import apiConstant from '../../utils/constants/apiConstant';

type EmployeeListScreen = {
    navigation: any;
};

export type EmployeeListType = {
    userId: number;
    employeeId: string;
    image: string;
    name: string;
    email: string;
    mobileNumber: string;
    isActive: boolean;
    manager: string;
    designation: string;
};

const EmployeeListScreen: React.FC<EmployeeListScreen> = ({ navigation }) => {
    const [fetching, setFetching] = useState<boolean>(false);
    const [listData, setListData] = useState<EmployeeListType[]>([]);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
    const [currentPage, setCurrentPage] = useState<number>(1);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setFetching(true);
        const startIndex = (currentPage - 1) * 10;
        httpGet(`${apiConstant.ALL_USER}?start=${startIndex}&limit=${10}`)
            .then((response: any) => {
                const empList = JSON.parse(response)?.data?.data;
                if (empList) {
                    setListData(empList);
                }
                setFetching(false);
            })
            .catch((err) => {
                console.error('Fetch error:', err);
                setFetching(false);
                errorHandlerClicked(true, `${stringText.SomethingWentwrong}`);
            });
    }, []);

    const handlePageChange = (pageNumber: number) => {
        setCurrentPage(pageNumber);
    };

    const renderItem = ({ item }: { item: EmployeeListType }) => (
        <RNButton
            ActiveOpacity={0.8}
            handleOnPress={() =>
                navigation.push(navigationStringText.EmployeeDetails, { empId: item.userId })
            }
        >


            <EmpListCard
                mobileNumber={item.mobileNumber}
                email={item.email}
                userId={item.userId}
                designation={item.designation}
                name={item.name}
                employeeId={item.employeeId}
                image={item.image}
                manager={item.manager}
                isActive={item.isActive}
            />
        </RNButton>
    );

    const keyExtractor = (item: EmployeeListType) => item.userId.toString(); // Assuming userId is unique

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <View
                style={styles.mainContainer}
            >
                <SearchBar
                    handelOnChange={(text) => setSearchQuery(text)}
                    value={searchQuery}
                />
            </View>
            {listData.filter((item) =>
                item.name.toLowerCase().includes(searchQuery.toLowerCase().trim())
            ).length === 0 ? (

                !fetching &&
                <View
                    style={styles.noDataView}
                >
                    <RNText style={styles.titleText}>{stringText.DataNotAvailable}</RNText>
                </View>
            ) : (
                <FlatList
                    data={listData.filter((item) =>
                        item.name.toLowerCase().includes(searchQuery.toLowerCase().trim())
                    )}
                    renderItem={renderItem}
                    contentContainerStyle={{ paddingBottom: 30 }}
                    keyExtractor={keyExtractor}
                />
            )}
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};

export default EmployeeListScreen;
