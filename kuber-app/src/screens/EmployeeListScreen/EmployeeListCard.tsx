import React from "react"
import { View } from "react-native"
import RNText from "../../component/RNText"
import FontAwesome from "react-native-vector-icons/FontAwesome"
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons"
import { color } from "../../utils/constants/color"
import styles from "./styles"
import RNImage from "../../component/RNImage"

const EmpListCard: React.FC<{
    userId: number;
    image: string;
    employeeId: string;
    name: string;
    designation: string;
    email: string;
    mobileNumber: string;
    manager: string;
    isActive: number | any;
}> = ({ userId, employeeId, image, name, email, mobileNumber, isActive, designation }) => {
    
    return (
        <View style={styles.empListCardWrapper}>
            <View style={styles.proileImgWrapper}>
                {image && (
                    <RNImage
                        source={{ uri: image }}
                        style={styles.proileImg}
                    />
                )}
                <RNText style={styles.empIdText}>
                    {employeeId}
                </RNText>
            </View>
            <View style={styles.detailsWrapper}>
                <RNText style={styles.nameText}>{name}</RNText>
                <RNText style={styles.designationText}>
                    {designation}
                </RNText>
                <View style={styles.iconDetailsWrapper}>
                    <FontAwesome
                        name="phone"
                        size={16}
                        color={color.BLACK + 60}
                    />
                    <RNText style={styles.iconTextText}>
                        {mobileNumber}
                    </RNText>
                </View>
                <View style={styles.iconDetailsWrapper}>
                    <MaterialCommunityIcons
                        name="email"
                        size={16}
                        color={color.BLACK + 60}
                    />
                    <RNText style={styles.iconTextText}>
                        {email} 
                    </RNText>
                </View>
            </View>
        </View>
    )
}

export default EmpListCard;