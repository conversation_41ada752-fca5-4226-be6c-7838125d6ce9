import React from 'react';
import { FlatL<PERSON>, <PERSON>ing, ScrollView, View } from 'react-native';
import RNText from '../../component/RNText';
import styles from './styles';
import ImageViewer from '../../component/ImageViewer';
import FontAwesome from "react-native-vector-icons/FontAwesome"
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons"
import { color } from '../../utils/constants/color';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import { stringText } from '../../utils/constants/stringsText';
import RNButton from '../../component/RNButton';

const DRsDetailsCard: React.FC<{ DRsData: any, navigation: any }> = ({ DRsData, navigation }) => {
    return (
        <View style={styles.cardWrapper}>
            {DRsData?.image_path ? (
                <ImageViewer imageLink={DRsData?.image_path} style={styles.profileWrapper} />
            ) : (
                <View style={[styles.profileWrapper]}></View>
            )}
            <View
                style={styles.mainInfoView}
            >
                {DRsData?.id ? (
                    <RNText style={styles.title}>
                        {DRsData?.first_name + ' ' + DRsData?.last_name}
                    </RNText>
                ) : (
                    <View
                        style={[
                            styles.titleSkeleton,
                            { padding: 14, width: '60%' }
                        ]}
                    ></View>
                )}
                {DRsData?.id ? (
                    <RNText style={styles.subTitle}>
                        {DRsData?.designation}
                    </RNText>
                ) : (
                    <View
                        style={[
                            styles.titleSkeleton,
                            { padding: 12, width: '40%' }
                        ]}
                    ></View>
                )}
            </View>
            <View
                style={styles.contactInfoView}
            >
                <View >
                    {DRsData?.id ? (
                        <View style={styles.contactContainer}>
                            <MaterialCommunityIcons
                                name="email"
                                size={16}
                                color={color.BLACK}
                            />
                            <RNText style={styles.PlaceValueContact} onPress={() => Linking.openURL(`mailto:${DRsData?.DRSEmployees?.userDetails?.email}`)}>
                                {DRsData?.username}
                            </RNText>
                        </View>
                    ) : (
                        <View
                            style={[
                                styles.titleSkeleton,
                                styles.titleSkeletonView,
                            ]}
                        ></View>
                    )}
                </View>
                <View >
                    {DRsData?.id ? (
                        <View style={styles.contactContainer}>
                            <FontAwesome
                                name="phone"
                                size={16}
                                color={color.BLACK}
                            />
                            <RNText style={styles.PlaceValueContact} onPress={() => { Linking.openURL(`tel:${DRsData?.mobile_no}`); }}>
                                {DRsData?.mobile_no}
                            </RNText>
                        </View>
                    ) : (
                        <View
                            style={[
                                styles.titleSkeleton,
                                styles.titleSkeletonView,
                            ]}
                        ></View>
                    )}
                </View>
            </View>
            <View
                style={styles.basicInfoView}
            >
                <View style={styles.rowDirection}>
                    <View style={styles.placeHolderValueWrapper}>
                        {DRsData?.id ? (
                            <>
                                <RNText style={styles.placeHolder}>
                                    {' '} {stringText.EmployeeID} {' '}
                                </RNText>
                                <RNText style={styles.PlaceValue}>
                                    {DRsData?.employee_id}
                                </RNText>
                            </>
                        ) : (
                            <>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        styles.titleSkeletonBasicInfoView1
                                    ]}
                                ></View>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        styles.titleSkeletonBasicInfoView2
                                    ]}
                                ></View>
                            </>
                        )}
                    </View>
                    <View style={styles.placeHolderValueWrapper}>
                        {DRsData?.id ? (
                            <>
                                <RNText style={styles.placeHolder}>
                                    {' '} {stringText.WorkStation}{' '}
                                </RNText>
                                <RNText style={styles.PlaceValue}>
                                    {DRsData?.workStation === null || DRsData?.workStation == undefined ? '-' : DRsData.workStation}
                                </RNText>
                            </>
                        ) : (
                            <>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        styles.titleSkeletonBasicInfoView1
                                    ]}
                                ></View>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        styles.titleSkeletonBasicInfoView2
                                    ]}
                                ></View>
                            </>
                        )}
                    </View>
                </View>
                <View style={styles.rowDirection}>
                    <View style={styles.placeHolderValueWrapper}>
                        {DRsData?.id ? (
                            <>
                                <RNText style={styles.placeHolder}>
                                    {' '} {stringText.UpcomingLeave} {' '}
                                </RNText>
                                <RNText style={styles.PlaceValue}>
                                    {DRsData?.upcomingPlannedLeave === null ? 'NA' : DRsData?.upcomingPlannedLeave}
                                </RNText>
                            </>
                        ) : (
                            <>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        styles.titleSkeletonBasicInfoView1
                                    ]}
                                ></View>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        styles.titleSkeletonBasicInfoView2
                                    ]}
                                ></View>
                            </>
                        )}
                    </View>
                    <View style={styles.placeHolderValueWrapper}>
                        {DRsData?.id ? (
                            <>
                                <RNText style={styles.placeHolder}>
                                    {' '} {stringText.CurrentProject} {' '}
                                </RNText>
                                <RNText style={styles.PlaceValue}>
                                    {DRsData?.projectName === null ? '-' : DRsData?.projectName}
                                </RNText>
                            </>
                        ) : (
                            <>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        styles.titleSkeletonBasicInfoView1
                                    ]}
                                ></View>
                                <View
                                    style={[
                                        styles.titleSkeleton,
                                        styles.titleSkeletonBasicInfoView2
                                    ]}
                                ></View>
                            </>
                        )}
                    </View>
                </View>
            </View>
            {DRsData?.DRS && DRsData.DRS.length > 0 &&
                <View
                    style={styles.drsCardView}
                >
                    <RNText style={styles.placeHolder}>
                        {`${DRsData?.first_name}'s DRs`}
                    </RNText>
                    <FlatList
                        data={DRsData?.DRS}
                        keyExtractor={(item, index) => index.toString()}
                        renderItem={({ item }) => (
                            <View style={{ marginHorizontal: 10 }}>
                                <RNButton
                                    ActiveOpacity={0.6}
                                    handleOnPress={() =>
                                        navigation.push(navigationStringText.EmployeeDetails, { empId: item.userId })
                                    }
                                >
                                    <View style={styles.drsCard}>
                                        <ImageViewer imageLink={item?.imagePath} style={styles.drsProfileWrapper} />
                                        <RNText style={styles.drsText}>{item?.employeeName}</RNText>
                                    </View>
                                </RNButton>
                            </View>
                        )}
                        horizontal
                        showsHorizontalScrollIndicator={false}
                    />
                </View>
            }
        </View>
    );
};

export default DRsDetailsCard;
