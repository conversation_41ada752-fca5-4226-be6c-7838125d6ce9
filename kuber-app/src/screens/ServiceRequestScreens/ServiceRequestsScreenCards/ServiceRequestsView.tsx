import React, { Ref } from 'react';
import {
    ActivityIndicator,
    FlatList,
    Keyboard,
    SafeAreaView,
    View
} from 'react-native';
import Ionicons from 'react-native-vector-icons/MaterialIcons';
import styles from './styles';
import { color } from '../../../utils/constants/color';
import ServiceRequestCard from './ServiceRequestCard';
import { ServiceDataTypes } from '../../EditServiceRequests';
import SearchBar from '../../../component/SearchBar';
import { Dropdown } from 'react-native-element-dropdown';
import { stringText } from '../../../utils/constants/stringsText';
import Spinner from '../../../component/Loader';
import ErrorHandlerPopup from '../../../component/ErrorHandlerPopup';
import RNText from '../../../component/RNText';
import RNButton from '../../../component/RNButton';

const statusTypeData = [
    { label: 'All', value: -1 },
    { label: "Open", value: 1 },
    { label: "In progress", value: 2 },
    { label: "Won't fix", value: 4 },
    { label: "Closed", value: 5 },
    { label: "Re-open", value: 6 }
];

const priorityTypeData = [
    { label: 'All', value: -1 },
    { label: 'Low', value: 0 },
    { label: 'Normal', value: 1 },
    { label: 'High', value: 2 },
    { label: 'Urgent', value: 3 },
    { label: 'Immediate', value: 4 }
];

type Props = {
    GoToEditServiceRequests: (srId: string) => void;
    GoToAddServiceRequests: () => void;
    serviceData: ServiceDataTypes[];
    queryStr: string;
    handelOnChange: (value: string) => void;
    handelOnChangeStatus: (label: string, value: number) => void;
    handelOnChangePriority: (label: string, value: number) => void;
    fetching: boolean;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
    handelOnScrollEnd: () => void;
    handleRefresh: () => void;
    isRefreshing: boolean;
    statusCode: { label: string; value: number };
    fetchingLoadMore: boolean;
    priorityCode: { label: string; value: number };
    flatListRef: any;
};

const ServiceRequestsView: React.FC<Props> = ({
    GoToEditServiceRequests,
    GoToAddServiceRequests,
    serviceData,
    queryStr,
    handelOnChange,
    handelOnChangeStatus,
    handelOnChangePriority,
    fetching,
    fetchingLoadMore,
    errorHandlerVisibility,
    errorHandlerMessage,
    errorHandlerClicked,
    handelOnScrollEnd,
    handleRefresh,
    isRefreshing,
    statusCode,
    priorityCode,
    flatListRef
}) => {
    const renderItem = ({ item }: { item: ServiceDataTypes }) => (
        <ServiceRequestCard
            serviceRequestData={item}
            GoToEditServiceRequests={GoToEditServiceRequests}
        />
    );

    const renderFooter = () => {
        return (
            <View style={styles.activityIndicatorContainerSR}>
                <ActivityIndicator
                    animating={fetchingLoadMore}
                    size="large"
                    color={color.DARK_BLUE}
                    style={styles.spinnerStyle1SR}
                />
            </View>
        );
    };

    return (
        <SafeAreaView style={styles.mainSafeAreaView}>
            <View style={{ paddingTop: 16, paddingHorizontal: 16 }}>
                <SearchBar handelOnChange={handelOnChange} value={queryStr} />
                <View style={styles.filterWrapper}>
                    <Dropdown
                        // containerStyle={{backgroundColor:color.WHITE}}
                        style={styles.dropDown}
                        data={statusTypeData}
                        placeholderStyle={{ color: color.BLACK }}
                        itemTextStyle={{ color: color.BLACK }}
                        selectedTextStyle={{ color: color.BLACK }}
                        maxHeight={600}
                        value={statusCode}
                        labelField="label"
                        valueField="value"
                        placeholder={stringText.FilterStatus}
                        onChange={(item) => {
                            Keyboard.dismiss();
                            handelOnChangeStatus(item.label, Number(item.value))
                        }
                        }
                    />
                    <Dropdown
                        style={styles.dropDown}
                        data={priorityTypeData}
                        placeholderStyle={{ color: color.BLACK }}
                        itemTextStyle={{ color: color.BLACK }}
                        selectedTextStyle={{ color: color.BLACK }}
                        maxHeight={600}
                        value={priorityCode}
                        labelField="label"
                        valueField="value"
                        placeholder={stringText.FilterPriority}
                        onChange={(item) => {
                            Keyboard.dismiss();
                            handelOnChangePriority(
                                item.label,
                                Number(item.value)
                            )
                        }
                        }
                    />
                </View>
            </View>

            {serviceData.length > 0 ? (
                <FlatList
                    ref={flatListRef}
                    data={serviceData}
                    renderItem={renderItem}
                    keyExtractor={(item: ServiceDataTypes) =>
                        item.srId.toString()
                    }
                    onEndReached={handelOnScrollEnd}
                    onEndReachedThreshold={0.6}
                    style={styles.serviceRequestsCardWrapper}
                    showsVerticalScrollIndicator={false}
                    refreshing={isRefreshing}
                    onRefresh={handleRefresh}
                    ListFooterComponent={renderFooter}
                />
            ) : (

                !fetching &&

                <View
                    style={{
                        flex: 1,
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}
                >
                    <RNText style={styles.titleText}>No Data Found</RNText>
                </View>


            )}

            <RNButton
                style={styles.ButtonView}
                handleOnPress={GoToAddServiceRequests}
            >
                <Ionicons name="add" size={24} color={color.WHITE} />
            </RNButton>

            <Spinner animating={fetching} />
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </SafeAreaView>
    );
};

export default ServiceRequestsView;
