import React, { useEffect, useRef, useState } from 'react';
import ServiceRequestsView from './ServiceRequestsView';
import { navigationStringText } from '../../../utils/constants/navigationStringText';
import { httpGet } from '../../../utils/http';
import { stringText } from '../../../utils/constants/stringsText';
import { ServiceDataTypes } from '../../EditServiceRequests';
import { useIsFocused } from '@react-navigation/native';
import apiConstant from '../../../utils/constants/apiConstant';

export type Props = {
    navigation: any;
    route: { params: { refresh: boolean } };
};

const ServiceRequestsScreenCards = (props: Props) => {
    const { navigation, route } = props;
    const [fetching, setFetching] = useState<boolean>(false);
    const flatListRef = useRef(null);

    const [fetchingLoadMore, setFetchingLoadMore] = useState<boolean>(false);
    const [filteredData, setFilteredData] = useState<ServiceDataTypes[]>([]);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [page, setPage] = useState(0);
    const [queryStr, setQueryStr] = useState<string>('');
    const [statusCode, setStatusCode] = useState<{
        label: string;
        value: number;
    }>({ label: '', value: -1 });
    const [priorityCode, setPriorityCode] = useState<{
        label: string;
        value: number;
    }>({ label: '', value: -1 });
    const [isRefreshing, setIsRefreshing] = useState(false);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    const fetchData = (refresh = false) => {
        !refresh && setFetching(true);

        const url = `${apiConstant.SERVICEREQUESTCARD}?page=${0}&pageSize=30`;
        httpGet(url)
            .then((response: any) => {
                const srData = JSON.parse(response)?.data?.data;
                const filteredData = srData.sort((a: any, b: any) => {
                    return (
                        new Date(b.created_at).getTime() -
                        new Date(a.created_at).getTime()
                    );
                });
                if (refresh) {
                    !refresh && setFetching(false);
                    setIsRefreshing(false);
                    setFilteredData(filteredData);
                    return;
                } else {
                    !refresh && setFetching(false);
                    setIsRefreshing(false);
                    setFilteredData((prevData) => [
                        ...prevData,
                        ...filteredData
                    ]);
                    return;
                }
            })
            .catch((err) => {
                setFetching(false);
                setIsRefreshing(false);
                if (err?.response?.data?.message) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    useEffect(() => {
        fetchData();
    }, []);

    useEffect(() => {
        fetchData(true);
    }, [route.params?.refresh]);

    const searchByQuery = (query: string, status: number, priority: number) => {
        setFetching(true);
        httpGet(
            `${apiConstant.SERVICEREQUESTCARD}?page=0&pageSize=30&searchString=${query}&status=${
                status !== -1 ? status : ''
            }&priority=${priority !== -1 ? priority : ''}`
        )
            .then((response: any) => {
                const srData = JSON.parse(response)?.data?.data;
                setFilteredData(srData.length>0?srData:[]);
                setFetching(false);

                if (flatListRef.current) {
                    flatListRef.current.scrollToOffset({
                        animated: true,
                        offset: 0
                    });
                }
            })
            .catch((err) => {
                setFetching(false);
                if (err?.response?.data?.message) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };
    const isFocused = useIsFocused();

    useEffect(() => {
        if (isFocused) {
            setPage(0)
            fetchData(true);
            setQueryStr('');
            setStatusCode({ label: 'All', value: -1 });
            setPriorityCode({ label: 'All', value: -1 });
            if (flatListRef.current) {
                flatListRef.current.scrollToOffset({
                    animated: true,
                    offset: 0
                });
            }
        }
    }, [isFocused]);

    const handleRefresh = () => {
        setIsRefreshing(true);
        fetchData(true);
        setQueryStr('');
        setStatusCode({ label: 'All', value: -1 });
        setPriorityCode({ label: 'All', value: -1 });
 
    };

    const handleLoadMore = () => {
        if (filteredData.length < 30) {
            return;
        } else if (
            !fetchingLoadMore &&
            queryStr == '' &&
            statusCode.value == -1 &&
            priorityCode.value == -1
        ) {
            setFetchingLoadMore(true);
            const nextPage = page + 1;
            const url = `${apiConstant.SERVICEREQUESTCARD}?page=${nextPage}&pageSize=30`;

            httpGet(url)
                .then((response: any) => {
                    const srData = JSON.parse(response)?.data?.data;
                    setFilteredData((prevData) => [...prevData, ...srData]);
                    setPage(nextPage);
                    setFetchingLoadMore(false);
                })
                .catch((err) => {
                    setFetchingLoadMore(false);
                    if (err?.response?.data?.message) {
                        errorHandlerClicked(true, err?.response?.data?.message);
                    } else {
                        errorHandlerClicked(
                            true,
                            `${stringText.SomethingWentwrong}`
                        );
                    }
                });
        }
    };

    const handelOnChange = (searchStr: string) => {
        setQueryStr(searchStr);
        if(searchStr.trim().length>0){
            searchByQuery(
            searchStr.trim(),
            Number(statusCode.value),
            Number(priorityCode.value)     
        );
    }
    };

    const handelOnChangeStatus = (label: string, value: number) => {
        setStatusCode({ label, value });
        searchByQuery(queryStr, value, priorityCode.value);
    };

    const handelOnChangePriority = (label: string, value: number) => {
        setPriorityCode({ label, value });
        searchByQuery(queryStr, statusCode.value, value);
    };

    const GoToEditServiceRequests = (srId?: string) => {
        navigation.navigate(navigationStringText.EditServiceRequests, {
            srId: srId
        });
    };

    const GoToAddServiceRequests = () => {
        navigation.navigate(navigationStringText.EditServiceRequests);
    };

    return (
        <ServiceRequestsView
            serviceData={filteredData}
            fetching={fetching}
            statusCode={statusCode}
            priorityCode={priorityCode}
            queryStr={queryStr}
            handleRefresh={handleRefresh}
            isRefreshing={isRefreshing}
            GoToAddServiceRequests={GoToAddServiceRequests}
            GoToEditServiceRequests={GoToEditServiceRequests}
            handelOnScrollEnd={handleLoadMore}
            handelOnChange={handelOnChange}
            handelOnChangeStatus={handelOnChangeStatus}
            handelOnChangePriority={handelOnChangePriority}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
            fetchingLoadMore={fetchingLoadMore}
            flatListRef={flatListRef}
        />
    );
};

export default ServiceRequestsScreenCards;
