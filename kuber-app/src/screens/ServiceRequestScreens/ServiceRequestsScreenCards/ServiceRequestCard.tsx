import RNText from '../../../component/RNText';
import styles from './styles';
import { ServiceDataTypes } from '../../EditServiceRequests';
import { View } from 'react-native';
import { stringText } from '../../../utils/constants/stringsText';
import RNButton from '../../../component/RNButton';

const statusData = [
    { label: "Open", value: 1 },
    { label: "In progress", value: 2 },
    { label: "Won't fix", value: 4 },
    { label: "Closed", value: 5 },
    { label: "Re-open", value: 6 }
];

const ServiceRequestCard: React.FC<{
    serviceRequestData: ServiceDataTypes;
    GoToEditServiceRequests: (srId: string | number | null) => void;
}> = ({ serviceRequestData, GoToEditServiceRequests }) => {


    return (
        <View
            key={serviceRequestData?.id}
            style={{ paddingHorizontal: 16 }}
        >
            <View style={styles.serviceRequestsCard}>
                <View style={{ justifyContent: 'space-between', flexDirection: 'row', marginBottom: 10, paddingHorizontal: 20 }}>
                    <RNText style={styles.srIdText}>
                        {serviceRequestData?.srId}
                    </RNText>
                    <View
                        style={[
                            styles.priorityWrapper,
                            {
                                opacity:
                                    serviceRequestData?.status != 1 ? 1 : 1
                            }
                        ]}
                    >
                        <RNText style={styles.priorityText}>
                            {statusData[statusData.findIndex((val: any) => val.value == serviceRequestData?.status)]?.label}
                        </RNText>
                    </View>
                </View>

                <View style={styles.horizantolUpperLineBreak} />
                <RNText style={styles.titleText}>
                    {serviceRequestData?.title}
                </RNText>
                <View style={styles.horizantolLowerLineBreak} />

                <View style={styles.srDetails}>
                    <View style={{ width: '33%', alignItems: 'center' }}>
                        <RNText style={styles.srDetailsLabel}>{stringText.Department}</RNText>
                        <RNText style={styles.srTypeText}>
                            {serviceRequestData?.department?.dept_name}
                        </RNText>
                    </View>
                    <View style={styles.lineBreak} />
                    <View style={{ width: '33%', alignItems: 'center' }}>
                        <RNText style={styles.srDetailsLabel}>{stringText.Type}</RNText>
                        <RNText style={styles.srTypeText}>
                            {serviceRequestData?.issues?.title}
                        </RNText>
                    </View>
                    <View style={styles.lineBreak} />
                    <View style={{ width: '33%', alignItems: 'center' }}>
                        <RNText style={styles.srDetailsLabel}>{stringText.Priority}</RNText>
                        <RNText style={styles.priorityText}>
                            {serviceRequestData?.priority == 0
                                ? 'Low'
                                : serviceRequestData?.priority == 1
                                    ? 'Normal'
                                    : serviceRequestData?.priority == 2
                                        ? 'High'
                                        : serviceRequestData?.priority == 3
                                            ? 'Urgent'
                                            : serviceRequestData?.priority == 4
                                                ? 'Immediate'
                                                : 'NA'}
                        </RNText>
                    </View>
                </View>

                <View style={{ gap: 10, paddingHorizontal: 20, alignItems: 'flex-end' }}>
                    <RNButton
                        ActiveOpacity={0.8}
                        handleOnPress={() => GoToEditServiceRequests(serviceRequestData.id)}
                        style={[styles.flexRow, { opacity: 1, paddingTop: 18 }]}
                    >
                        <RNText style={styles.createdAtText}>{stringText.ViewMore}</RNText>
                    </RNButton>
                </View>
            </View>
        </View>
    );
};
export default ServiceRequestCard;
