import { StyleSheet } from 'react-native';
import { color } from '../../../utils/constants/color';

const styles = StyleSheet.create({
    mainSafeAreaView: {
        flex: 1,
    },

    filterWrapper: {
        flexDirection: 'row',
        marginTop:8,
        gap:20,
    },

    dropDown: {
        flex:1,
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK,
        justifyContent: 'center',
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 5,
        height: 40,
        backgroundColor:color.WHITE
    },
    serviceRequestsCardWrapper: {
        marginVertical: 10,
    },
    ButtonView: {
        position: 'absolute',
        right: 20,
        bottom: 20,
        height: 56,
        width: 56,
        resizeMode: 'cover',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: color.ACCENT_BLUE,
        borderRadius: 100
    },

    // Service Requests Card style

    serviceRequestsCard: {
        backgroundColor: color.WHITE,
        marginVertical: 6,
        elevation: 4,
        borderRadius: 6,
        justifyContent: 'space-between',
        paddingVertical:12
    },
    flexCenter: {
        alignItems: 'center',
        gap: 10
    },
    flexRow:{
        flexDirection:'row',
        alignItems:'center',
        gap:4
    },
    serviceRequestsCardLeftContent: {
        flexDirection: 'row',
        gap: 20,
        paddingHorizontal: 12,
        maxWidth: '70%',
    },
    // srIdWrapper: {
    //     padding: 10,
    //     borderRadius: 6,
    //     backgroundColor: color.DARK_BLUE,
    //     maxWidth:120,
    // },
    srIdText: {
        fontSize: 16,
        fontWeight: 'bold',
        letterSpacing: 2,
        color: color.BLACK,
        flexWrap:'wrap',
        // textAlign:'center',
        width:'60%'
    },
    priorityWrapper: {
        shadowOffset: { width: 4, height: 4 },
        shadowOpacity:0.2,
        shadowColor: 'black',
        alignItems: 'flex-end',
        width:'30%'
    },
    priorityText: {
        fontSize: 14,
        fontWeight: '400',
        color: color.BLACK
    },
    srTitleWrapper: {
        flex: 1,
        gap:6,
        justifyContent:'center'
    },
    titleText: {
        fontSize: 16,
        fontWeight: '500',
        color: color.BLACK,
        flexWrap: 'wrap',
        padding:12,
        paddingHorizontal:20
    },
    srDetails:{
        marginTop:20,
        flexDirection:'row',
        justifyContent:'space-between',
    },
    srDetailsLabel:{
        fontSize:14,
        fontWeight:'800',
        color: color.BLACK,marginBottom:2
    },
    departmentText: {
        fontSize: 14,
        fontWeight: '400',
        color: color.BLACK
    },
    srTypeText: {
        fontSize: 14,
        fontWeight: '400',
        color: color.BLACK,
        paddingHorizontal:5
    },
    lineBreak:{
        width:1,
        height:'100%',
        backgroundColor:color.BLACK+20
    },
    horizantolLowerLineBreak:{
        height:1,
        width:'100%',
        backgroundColor:color.BLACK+20,
        alignSelf:'center',
    },
    horizantolUpperLineBreak:{
        height:1,
        width:'100%',
        backgroundColor:color.BLACK,
        alignSelf:'center',
        // marginHorizontal:8
    },
    serviceRequestsCardRightContent: {
        paddingHorizontal: 4,
        alignItems:'center',
    },
    createdByText: {
        fontSize: 12,
        fontWeight: '400',
        color: color.BLACK
    },
    createdAtText: {
        fontSize: 12,
        fontWeight: 'bold',
        color: color.DARK_BLUE,
        textAlign: 'center',
        textDecorationLine:'underline'
    },
    activityIndicatorContainerSR: {
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical:10
    },
    spinnerStyleSR: {
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        opacity: 0.8,
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(0,0,0,0)'
    },
    spinnerStyle1SR: {
        alignItems: 'center',
        justifyContent: 'center',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
    },
});

export default styles;
