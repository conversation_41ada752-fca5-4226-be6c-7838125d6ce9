import React, { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, View } from 'react-native';
import RNText from '../../component/RNText';
import { stringText } from '../../utils/constants/stringsText';
import { color } from '../../utils/constants/color';
import { httpGet } from '../../utils/http';
import RNActivityIndicator from '../../component/Loader';
import apiConstant from '../../utils/constants/apiConstant';
import RNImage from '../../component/RNImage';
import styles from './styles';
import { RadioButton } from 'react-native-paper';
import { Dropdown } from 'react-native-element-dropdown';
import ImageViewer from '../../component/ImageViewer';

type BasicInfoScreen = {
    navigation: any;
};

const serviceAgreementData = [
    { label: "24 Month", value: 1 },
    { label: "18 Month", value: 2 },
    { label: "12 Month", value: 3 },
]

const ProbatationPeriodData = [
    { label: "3 Month", value: 3 },
    { label: "6 Month", value: 6 },
    { label: "9 Month", value: 9 },
    { label: "12 Month", value: 12 },
]

const BloodGroupData = [
    { label: "A Positive", value: "A+" },
    { label: "A Negative", value: "A-" },
    { label: "B Positive", value: "B+" },
    { label: "B Negative", value: "B-" },
    { label: "O Positive", value: "O+" },
]

const BasicInfoScreen: React.FC<BasicInfoScreen> = () => {
    const [userId, setUserId] = useState<number | null>(null);
    const [yourInfo, setYourInfo] = useState<any>('');
    const [maritialDropdown, setMaritialDropdown] = useState<[]>([]);
    const [contractEmployee, setContractEmployee] = useState<string>("");
    const [underProbation, setUnderProbation] = useState<string>("");

    const [probationPeriod, setProbationPeriod] = useState<any>('');
    const [isFocusProbationPeriod, setIsFocusProbationPeriod] = useState<boolean>(false);
    const [bloodGroup, setBloodGroup] = useState<any>('');
    const [isFocusBloodGroup, setIsFocusBloodGroup] = useState<boolean>(false);
    const [maritialStatus, setMaritialStatus] = useState<any>('');
    const [isFocusMaritialStatus, setIsFocusMaritialStatus] = useState<boolean>(false);

    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    const RenderProbationPeriod = () => {
        if (probationPeriod || isFocusProbationPeriod) {
            return (
                <RNText style={[styles.label, isFocusProbationPeriod && { color: color.ACCENT_BLUE }]}>
                    Probation Period
                </RNText>
            );
        }
        return null;
    };

    const RenderBloodGroup = () => {
        if (bloodGroup || isFocusBloodGroup) {
            return (
                <RNText style={[styles.label, isFocusBloodGroup && { color: color.ACCENT_BLUE }]}>
                    Blood Group
                </RNText>
            );
        }
        return null;
    };

    const RenderMaritialStatus = () => {
        if (maritialStatus || isFocusMaritialStatus) {
            return (
                <RNText style={[styles.label, isFocusMaritialStatus && { color: color.ACCENT_BLUE }]}>
                    Maritial Status
                </RNText>
            );
        }
        return null;
    };

    useEffect(() => {
        getUserInfo();
        getMaritialStatus();
        if (userId != null) {
            basicInfo();
            // getAllEmployees();
        }
    }, [userId]);

    const getUserInfo = () => {
        // console.log("User Info get api");

        setFetching(true);
        httpGet(apiConstant.USER_INFO)
            .then((response: any) => {
                const yourInfodata = JSON.parse(response)?.data;
                setUserId(yourInfodata?.id)
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }

    const basicInfo = () => {
        // console.log("Basic Info get api");

        setFetching(true);
        httpGet(`/users/basic-info?userId=${userId}&isGlobal=false`)
            .then((response: any) => {
                const yourInfodata = JSON.parse(response)?.data;
                if (yourInfodata) {
                    // console.log(yourInfodata);
                    setYourInfo(yourInfodata);
                    setContractEmployee(yourInfodata?.is_contract_employee.toString());
                    setUnderProbation(yourInfodata?.under_probation.toString());
                    setBloodGroup(yourInfodata?.blood_group);
                    setMaritialStatus(yourInfodata?.marital_status);
                    setProbationPeriod(yourInfodata?.probation_period);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }

    const getAllEmployees = () => {
        setFetching(true);
        httpGet(apiConstant.GET_ALL_EMP)
            .then((response: any) => {
                const managerData = JSON.parse(response)?.data;
                if (managerData) {
                    // setManagerDropDown(managerData);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const getMaritialStatus = () => {
        setFetching(true);
        // console.log("marital-status get api");

        httpGet(`/users/marital-status`)
            .then((response: any) => {
                const maritialData = JSON.parse(response)?.data;
                if (maritialData) {
                    setMaritialDropdown(maritialData);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    return (
        <SafeAreaView>
            <ScrollView>
                <View style={styles.container}>
                    {!fetching && (
                        <View style={styles.Info}>

                            <ImageViewer imageLink={yourInfo?.image_path} style={styles.profileImage} />
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`First Name`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.first_name
                                        ? yourInfo.first_name
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Middle Name`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.middle_name
                                        ? yourInfo.middle_name
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Last Name`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.last_name
                                        ? yourInfo.last_name
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Father's Name`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.father_name
                                        ? yourInfo.father_name
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Mother's Name`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.mother_name
                                        ? yourInfo.mother_name
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Email`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.username
                                        ? yourInfo.username
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Alternate Email`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.email
                                        ? yourInfo.email
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Mobile No`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.mobile_no
                                        ? yourInfo.mobile_no
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Employee Id`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.employee_id
                                        ? yourInfo.employee_id
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Floor`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.floorName
                                        ? yourInfo.floorName
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Workstation`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.workStation
                                        ? yourInfo.workStation
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Office Time`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.officeTime
                                        ? new Date(`1970-01-01T${yourInfo.officeTime}:00+05:30`).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.titleText}>
                                    Contract Employee
                                </RNText>
                                <RadioButton.Group
                                    onValueChange={(newValue) =>
                                        setContractEmployee(newValue)
                                    }
                                    value={contractEmployee}
                                >
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }}
                                    >
                                        <RadioButton.Item
                                            mode="android"
                                            label="Yes"
                                            value="1"
                                            disabled={true}
                                            position="leading"
                                            style={{
                                                marginLeft: -15,
                                                marginBottom: -5
                                            }}
                                        />
                                        <RadioButton.Item
                                            mode="android"
                                            label="No"
                                            position="leading"
                                            style={{
                                                marginLeft: -15,
                                                marginBottom: -5
                                            }}
                                            value="0"
                                            disabled={true}
                                        />
                                    </View>
                                </RadioButton.Group>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.titleText}>
                                    Under Probation
                                </RNText>
                                <RadioButton.Group
                                    onValueChange={(newValue) =>
                                        setUnderProbation(newValue)
                                    }
                                    value={underProbation}
                                >
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }}
                                    >
                                        <RadioButton.Item
                                            mode="android"
                                            label="Yes"
                                            value="1"
                                            disabled={true}
                                            position="leading"
                                            style={{
                                                marginLeft: -15,
                                                marginBottom: -5
                                            }}
                                        />
                                        <RadioButton.Item
                                            mode="android"
                                            label="No"
                                            position="leading"
                                            style={{
                                                marginLeft: -15,
                                                marginBottom: -5
                                            }}
                                            value="0"
                                            disabled={true}
                                        />
                                    </View>
                                </RadioButton.Group>
                            </View>
                            <View style={{ position: 'relative' }}>
                                <RenderProbationPeriod />
                                <Dropdown
                                    style={[styles.dropdown, isFocusProbationPeriod && { borderColor: color.DARK_BLUE }]}
                                    placeholderStyle={styles.placeholderStyle}
                                    selectedTextStyle={styles.selectedTextStyle}
                                    data={ProbatationPeriodData}
                                    maxHeight={300}
                                    labelField="label"
                                    valueField="value"
                                    placeholder={!isFocusProbationPeriod ? 'Probation Period' : '...'}
                                    value={probationPeriod}
                                    disable={true}
                                    onFocus={() => setIsFocusProbationPeriod(true)}
                                    onBlur={() => setIsFocusProbationPeriod(false)}
                                    onChange={item => {
                                        setProbationPeriod(item.value);
                                        setIsFocusProbationPeriod(false);
                                    }}
                                />
                            </View>
                            <View style={{ position: 'relative' }}>
                                <RenderBloodGroup />
                                <Dropdown
                                    style={[styles.dropdown, isFocusBloodGroup && { borderColor: color.DARK_BLUE }]}
                                    placeholderStyle={styles.placeholderStyle}
                                    selectedTextStyle={styles.selectedTextStyle}
                                    // iconStyle={styles.iconStyle}
                                    data={BloodGroupData}
                                    // data={designationBandDropDown.map((item: any) => ({ label: item.band_title, value: item.id }))}
                                    maxHeight={300}
                                    labelField="label"
                                    valueField="value"
                                    placeholder={!isFocusBloodGroup ? 'Blood Group' : '...'}
                                    value={bloodGroup}
                                    disable={true}
                                    onFocus={() => setIsFocusBloodGroup(true)}
                                    onBlur={() => setIsFocusBloodGroup(false)}
                                    onChange={item => {
                                        setBloodGroup(item.value);
                                        setIsFocusBloodGroup(false);
                                    }}
                                />
                            </View>

                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Official Birth Date`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.official_birth_date
                                        ? new Date(yourInfo.official_birth_date).toLocaleDateString('en-GB')
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Actual Birth Date`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.birth_date
                                        ? new Date(yourInfo.birth_date).toLocaleDateString('en-GB')
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Age`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.age
                                        ? yourInfo.age
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ position: 'relative' }}>
                                <RenderMaritialStatus />
                                <Dropdown
                                    style={[styles.dropdown, isFocusMaritialStatus && { borderColor: color.DARK_BLUE }]}
                                    placeholderStyle={styles.placeholderStyle}
                                    selectedTextStyle={styles.selectedTextStyle}
                                    // iconStyle={styles.iconStyle}
                                    // data={serviceAgreementData}
                                    data={maritialDropdown.map((item: any) => ({ label: item.marital_status, value: item.id }))}
                                    maxHeight={300}
                                    labelField="label"
                                    valueField="value"
                                    placeholder={!isFocusMaritialStatus ? "Maritial Status" : '...'}
                                    value={maritialStatus}
                                    disable={true}
                                    onFocus={() => setIsFocusMaritialStatus(true)}
                                    onBlur={() => setIsFocusMaritialStatus(false)}
                                    onChange={item => {
                                        setMaritialStatus(item.value);
                                        setIsFocusMaritialStatus(false);
                                    }}
                                />
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Spouse Name`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.spouse_name
                                        ? yourInfo.spouse_name
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Spouse Mobile`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.spouse_mobile
                                        ? yourInfo.spouse_mobile
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Emergency Contact Person`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.contact_person
                                        ? yourInfo.contact_person
                                        : 'NA'}
                                </RNText>
                            </View>
                            <View style={{ gap: 5 }}>
                                <RNText style={styles.label}>
                                    {`Emergency Contact Person Phone`}
                                </RNText>
                                <RNText style={styles.infoField}>
                                    {yourInfo.contact_person_phone
                                        ? yourInfo.contact_person_phone
                                        : 'NA'}
                                </RNText>
                            </View>

                        </View>
                    )}
                </View>
            </ScrollView>
            {fetching && (
                <View style={styles.loader}>
                    <RNActivityIndicator animating={fetching} />
                </View>
            )}
        </SafeAreaView>
    );
};
export default BasicInfoScreen;
