import React, { useEffect, useState } from 'react';
import { FlatList, SafeAreaView, ScrollView, View } from 'react-native';
import RNText from '../../component/RNText';
import { stringText } from '../../utils/constants/stringsText';
import { color } from '../../utils/constants/color';
import { httpGet } from '../../utils/http';
import RNActivityIndicator from '../../component/Loader';
import apiConstant from '../../utils/constants/apiConstant';
import styles from './styles';
import { DataTable } from 'react-native-paper';

type OfficeInfoScreen = {
    navigation: any;
};

const OfficeInfoScreen: React.FC<OfficeInfoScreen> = () => {
    const [userId, setUserId] = useState<number | null>(null);
    const [yourLocationInfo, setYourLocationInfo] = useState<any>('');

    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        getUserInfo();
        if (userId != null) {
            basicInfo();
        }
    }, [userId]);

    const getUserInfo = () => {
        setFetching(true);
        httpGet(apiConstant.USER_INFO)
            .then((response: any) => {
                const yourInfodata = JSON.parse(response)?.data;
                setUserId(yourInfodata?.id)
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }

    const basicInfo = () => {
        setFetching(true);
        httpGet(`/users/basic-info?userId=${userId}&isGlobal=false`)
            .then((response: any) => {
                const yourInfodata = JSON.parse(response)?.data;
                if (yourInfodata) {
                    // console.log("yourInfodata?.companyLocationHistory", yourInfodata?.companyLocationHistory);
                    setYourLocationInfo(yourInfodata?.companyLocationHistory);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }

    const renderNoAttendance = ({ item }: { item: any }) => {
        return (
            <DataTable.Row style={[styles.tableValueTextWrapper]}>
                <RNText style={styles.tableValueText}>{item.locationName}</RNText>
                <RNText style={styles.tableValueText}>{new Date(item.start_date).toLocaleDateString('en-GB')} </RNText>
                <RNText style={styles.tableValueText}>{item.end_date ? new Date(item.end_date).toLocaleDateString('en-GB') : "N/A"}</RNText>
            </DataTable.Row>
        );
    };

    return (
        <SafeAreaView>
            <ScrollView>
                <View style={styles.container}>

                    <View style={styles.officeLocationInfo}>
                        <RNText style={styles.AddressTitle}>{`Office Location(s)`}</RNText>
                        <View style={styles.tableTitleWrapper}>
                            <RNText style={styles.tableTitleText}>Location</RNText>
                            <RNText style={styles.tableTitleText}>Start Date</RNText>
                            <RNText style={styles.tableTitleText}>End Date</RNText>
                        </View>
                        {
                            yourLocationInfo.length != 0 ?
                                <FlatList
                                    data={yourLocationInfo}
                                    renderItem={renderNoAttendance}
                                    keyExtractor={(item, index) => index.toString()}
                                    showsVerticalScrollIndicator={true}
                                    persistentScrollbar={true}
                                    indicatorStyle='black'
                                />
                                : <RNText style={styles.noDataText}>{stringText.DataNotAvailable}</RNText>
                        }
                    </View>
                </View>
            </ScrollView>
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};
export default OfficeInfoScreen;
