import React, { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, View } from 'react-native';
import RNText from '../../component/RNText';
import { stringText } from '../../utils/constants/stringsText';
import { color } from '../../utils/constants/color';
import { httpGet } from '../../utils/http';
import RNActivityIndicator from '../../component/Loader';
import apiConstant from '../../utils/constants/apiConstant';
import styles from './styles';
import { Dropdown } from 'react-native-element-dropdown';

type AddressInfoScreen = {
    navigation: any;
};

const AddressInfoScreen: React.FC<AddressInfoScreen> = () => {
    const [userId, setUserId] = useState<number | null>(null);
    const [yourAddressInfo, setYourAddressInfo] = useState<any>('');

    const [permanentStates, setPermanentStates] = useState<any>('');
    const [states, setStates] = useState<any>('');
    const [isFocusStates, setIsFocusStates] = useState<boolean>(false);
    const [stateDropDown, setStateDropDown] = useState<[]>([]);
    const [countries, setCountries] = useState<any>('');
    const [permanentCountries, setPermanentCountries] = useState<any>('');
    const [isFocusCountries, setIsFocusCountries] = useState<boolean>(false);
    const [countryDropDown, setCountryDropDown] = useState<[]>([]);

    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [employeeInfo, setEmployeeInfo] = useState<any>('');

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        getUserInfo();
        getCountryStateData();
        // console.log("use effect without condition");

    }, [])
    useEffect(() => {
        // console.log("use effect  condition");

        if (userId != null) {
            basicInfo();
        }
    }, [userId]);

    const getUserInfo = () => {
        // console.log("getUserInfo getUserInfo  getUserInfo");
        setFetching(true);
        httpGet(apiConstant.USER_INFO)
            .then((response: any) => {
                const yourAddressInfodata = JSON.parse(response)?.data;
                setUserId(yourAddressInfodata?.id)
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }

    const basicInfo = () => {
        // console.log("basicInfo basicInfo  basicInfo");
        setFetching(true);
        httpGet(`/users/basic-info?userId=${userId}&isGlobal=false`)
            .then((response: any) => {
                const yourAddressInfodata = JSON.parse(response)?.data;
                if (yourAddressInfodata) {
                    // console.log(yourAddressInfodata?.userAddress, "==========================================");
                    // console.log("yourAddressInfo.id_country", yourAddressInfodata?.userAddress.id_country, yourAddressInfodata?.userAddress.present_state);
                    setYourAddressInfo(yourAddressInfodata?.userAddress);
                    setCountries(yourAddressInfodata?.userAddress?.id_country);
                    setStates(yourAddressInfodata?.userAddress?.present_state);
                    setPermanentCountries(yourAddressInfodata?.userAddress?.id_country_permanent);
                    setPermanentStates(yourAddressInfodata?.userAddress?.permanent_state);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }

    const getCountryStateData = () => {
        // console.log("getCountryStateData getCountryStateData  getCountryStateData");
        setFetching(true);
        httpGet(apiConstant.BANK_INFO_COUNTRIES)
            .then((response: any) => {
                const countries = JSON.parse(response)?.data;
                // if (countries) {
                setCountryDropDown(countries);
                // }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });

        httpGet(apiConstant.BANK_INFO_STATE)
            .then((response: any) => {
                // console.log("BANK_INFO_STATE BANK_INFO_STATE  BANK_INFO_STATE");
                const states = JSON.parse(response)?.data;
                // if (states) {
                setStateDropDown(states);
                // }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });

    };

    const RenderCountries = () => {
        if (countries || isFocusCountries) {
            return (
                <RNText style={[styles.label, isFocusCountries && { color: color.ACCENT_BLUE }]}>
                    Country
                </RNText>
            );
        }
        return null;
    };

    const RenderStates = () => {
        if (states || isFocusStates) {
            return (
                <RNText style={[styles.label, isFocusStates && { color: color.ACCENT_BLUE }]}>
                    State
                </RNText>
            );
        }
        return null;
    };

    return (
        <SafeAreaView>
            <ScrollView>
                <View style={styles.container}>
                    <View style={styles.Info}>
                        <RNText style={styles.AddressTitle}>Present Address</RNText>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`Building/House name/Flat no`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.present_address
                                    ? yourAddressInfo.present_address
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`Locality(Area/street)`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.middle_name
                                    ? yourAddressInfo.middle_name
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ position: 'relative' }}>
                            <RenderCountries />
                            <Dropdown
                                style={[styles.dropdown, isFocusCountries && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                data={countryDropDown.map((item: any) => ({ label: item.country_name, value: item.id }))}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusCountries ? 'Country' : '...'}
                                value={countries}
                                disable={true}
                                onFocus={() => setIsFocusCountries(true)}
                                onBlur={() => setIsFocusCountries(false)}
                                onChange={item => {
                                    setCountries(item.value);
                                    setIsFocusCountries(false);
                                }}
                            />
                        </View>
                        <View style={{ position: 'relative' }}>
                            <RenderStates />
                            <Dropdown
                                style={[styles.dropdown, isFocusStates && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                data={stateDropDown.map((item: any) => ({ label: item.state_name, value: item.id }))}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusStates ? 'State' : '...'}
                                value={parseInt(states)}
                                disable={true}
                                onFocus={() => setIsFocusStates(true)}
                                onBlur={() => setIsFocusStates(false)}
                                onChange={item => {
                                    setStates(item.value);
                                    setIsFocusStates(false);
                                }}
                            />
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`City`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.present_city
                                    ? yourAddressInfo.present_city
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`Pincode`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.present_postal_code
                                    ? yourAddressInfo.present_postal_code
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`Nationality`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.mother_name
                                    ? yourAddressInfo.mother_name
                                    : 'NA'}
                            </RNText>
                        </View>
                    </View>
                    <View style={styles.Info}>
                        <RNText style={styles.AddressTitle}>Permanent Address</RNText>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`Building/House name/Flat no`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.permanent_address
                                    ? yourAddressInfo.permanent_address
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`Locality(Area/street)`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.middle_name
                                    ? yourAddressInfo.middle_name
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ position: 'relative' }}>
                            <RenderCountries />
                            <Dropdown
                                style={[styles.dropdown, isFocusCountries && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                data={countryDropDown.map((item: any) => ({ label: item.country_name, value: item.id }))}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusCountries ? 'Country' : '...'}
                                value={permanentCountries}
                                disable={true}
                                onFocus={() => setIsFocusCountries(true)}
                                onBlur={() => setIsFocusCountries(false)}
                                onChange={item => {
                                    setCountries(item.value);
                                    setIsFocusCountries(false);
                                }}
                            />
                        </View>
                        <View style={{ position: 'relative' }}>
                            <RenderStates />
                            <Dropdown
                                style={[styles.dropdown, isFocusStates && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                data={stateDropDown.map((item: any) => ({ label: item.state_name, value: item.id }))}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={!isFocusStates ? 'State' : '...'}
                                value={parseInt(permanentStates)}
                                disable={true}
                                onFocus={() => setIsFocusStates(true)}
                                onBlur={() => setIsFocusStates(false)}
                                onChange={item => {
                                    setStates(item.value);
                                    setIsFocusStates(false);
                                }}
                            />
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`City`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.permanent_city
                                    ? yourAddressInfo.permanent_city
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`Pincode`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.permanent_postal_code
                                    ? yourAddressInfo.permanent_postal_code
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {`Nationality`}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {yourAddressInfo.mother_name
                                    ? yourAddressInfo.mother_name
                                    : 'NA'}
                            </RNText>
                        </View>
                    </View>
                </View>
            </ScrollView>
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};
export default AddressInfoScreen;
