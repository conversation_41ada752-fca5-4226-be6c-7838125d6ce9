import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { color } from "../../utils/constants/color";
import BasicInfoScreen from './basicInfo';
import AddressInfoScreen from './address';
import OfficeInfoScreen from './officeLocation';

const Tab = createMaterialTopTabNavigator();

export type Props = {
    navigation: any
}

const YourInfoScreen = () => {

    return (
        <Tab.Navigator
            screenOptions={{
                tabBarLabelStyle: { fontSize: 12 },
                tabBarActiveTintColor: color.DARK_BLUE,
                tabBarInactiveTintColor: color.GREAYSCALE,
            }}
        >
            <Tab.Screen name="Basic" component={BasicInfoScreen} />
            <Tab.Screen name="Address" component={AddressInfoScreen} />
            <Tab.Screen name="Location" component={OfficeInfoScreen} />
        </Tab.Navigator>
    )
}
export default YourInfoScreen;