import React, { useState, SetStateAction, Dispatch } from 'react';
import {
    Text,
    View,
    ScrollView,
    SafeAreaView,
    TextInput,
    FlatList
} from 'react-native';
import RNText from '../../component/RNText';
import { Dropdown } from 'react-native-element-dropdown';
import Ionicons from 'react-native-vector-icons/Ionicons';
import styles from './style';
import { color } from '../../utils/constants/color';
import RNActivityIndicator from "../../component/Loader";

import { DataTable } from 'react-native-paper';
import Entypo from 'react-native-vector-icons/Entypo';
import { stringText } from '../../utils/constants/stringsText';
import ActionStatusPopUpView from '../ActionStatusPopUp/ActionStatusPopUpView';
import DRsOnLeavesCard from './DRsOnLeavesCard';
import SearchBar from '../../component/SearchBar';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';

const statusDropDownData = [
    { label: 'Pending', value: '0' },
    { label: 'Accepted', value: '1' },
    { label: 'Rejected', value: '2' }
];

export type Props = {
    navigation: any;
    postLeaveStatus: (leaveId: number, accept: boolean) => void;
    setStatus: (label: string, value: any) => void;
    status: { label: string; value: string };
    setMonth: (label: string, value: any) => void;
    setActionPopupVisibility: Dispatch<SetStateAction<boolean>>;
    fetching: Boolean;
    monthDropDownData: [];
    actionButtonClicked: (isVisible: boolean) => void;
    isVisible: boolean;
    month: { label: ''; value: number };
    searchText: string;
    setFetching: (isVisible: boolean) => void;

    setSearchText: (searchText: string) => void;
    leavdata: any;
    handelChangeStatus: (label: string, value: string) => void;
    handelChangeMonth: (label: string, value: number) => void;
    errorHandlerMessage: string;
    errorHandlerVisibility: boolean;
    errorHandlerClicked: () => void;
};

const DrsOnLeaveView = (props: Props) => {
    const {
        navigation,
        isVisible,
        errorHandlerClicked,
        errorHandlerMessage,
        errorHandlerVisibility,
        status,
        leavdata,
        fetching,
        setFetching,
        monthDropDownData,
        month,
        actionButtonClicked,
        setActionPopupVisibility,
        setSearchText,
        searchText,
        handelChangeStatus,
        handelChangeMonth,
        postLeaveStatus
    } = props;
    return (
        <SafeAreaView style={{ flex: 1 }}>
            <ScrollView contentContainerStyle={styles.scrollContainer}>
                <View style={styles.parentContainer}>
                    <View style={{ gap: 16, paddingHorizontal: 16 }}>
                        <RNText style={styles.headerText}>
                            {stringText.DRsOnLeaveInSelectedMonth}
                        </RNText>
                        <SearchBar
                            handelOnChange={(e) => setSearchText(e)}
                            value={searchText}
                        ></SearchBar>
                    </View>
                    <View style={styles.dropDownContainer}>
                        <Dropdown
                            style={styles.dropdown}
                            data={statusDropDownData}
                            maxHeight={600}

                            placeholderStyle={{ color: color.BLACK }}
                            itemTextStyle={{ color: color.BLACK }}
                            selectedTextStyle={{ color: color.BLACK }}
                            labelField="label"
                            valueField="value"
                            placeholder={stringText.SelectStatus}
                            value={status}
                            onChange={(item) => {
                                handelChangeStatus(item.label, item.value);
                            }}
                        />
                        <Dropdown
                            style={styles.dropdown}
                            data={monthDropDownData}
                            maxHeight={600}
                            placeholderStyle={{ color: color.BLACK }}
                            itemTextStyle={{ color: color.BLACK }}
                            selectedTextStyle={{ color: color.BLACK }}
                            labelField="label"
                            valueField="value"
                            placeholder={stringText.SelectMonth}
                            value={month}
                            onChange={(item) => {
                                handelChangeMonth(item.label, item.value);
                            }}
                        />
                    </View>
                    <ScrollView showsHorizontalScrollIndicator={false}>
                        {leavdata?.length > 0 ? (
                            <FlatList
                                data={leavdata}
                                renderItem={({ item }) => (
                                    <DRsOnLeavesCard
                                        navigation={navigation}
                                        actionButtonClicked={actionButtonClicked}
                                        isVisible={isVisible}
                                        postLeaveStatus={postLeaveStatus}
                                        setActionPopupVisibility={setActionPopupVisibility}
                                        data={item}
                                    />
                                )}
                                keyExtractor={(item, index) => index.toString()}
                            />

                        ) : (
                            !fetching &&

                            <View
                                style={styles.noDataView}
                            >
                                <RNText style={styles.titleText}>
                                    {stringText.DataNotAvailable}
                                </RNText>
                            </View>
                        )}
                    </ScrollView>
                </View>
            </ScrollView>
            <RNActivityIndicator animating={fetching} />
            <ErrorHandlerPopup
                errorHandlerClicked={errorHandlerClicked}
                errorHandlerMessage={errorHandlerMessage}
                visible={errorHandlerVisibility}
            />
        </SafeAreaView>
    );
};

export default DrsOnLeaveView;
