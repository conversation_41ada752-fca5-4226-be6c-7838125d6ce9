import React, { useEffect, useState } from "react";
import DrsOnLeaveView from "./DRsOnLeaveView";
import { httpGet } from "../../utils/http";
import { httpPost } from "../../utils/http";
import { stringText } from "../../utils/constants/stringsText";
import { successToast } from "../../component/SuccessToast";
import config from "../../utils/config";
import apiConstant from "../../utils/constants/apiConstant";

export type Props = {
    navigation: any;
};

interface LeaveDataType {
    desc: string;
    id: number;
    id_employee: string;
    id_leave_type: number;
    leave_end_date: string;
    leave_start_date: string;
    name: string;
    service_request_id: number;
    status: number;
}


const DRsOnLeaveScreenCards = (props: Props) => {
    const { navigation } = props;

    const [status, setStatus] = useState<{ lable: string, value: string }>({ lable: '', value: '' });
    const [month, setMonth] = useState<{ lable: string, value: number }>({ lable: '', value: -1 });

    const [searchText, setSearchText] = useState<string>('')

    const [fetching, setFetching] = useState<boolean>(false);
    const [actionPopupVisibility, setActionPopupVisibility] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
    const [leaveData, setLeaveData] = useState<LeaveDataType[]>([]);
    const [leaveStatus, setLeaveStatus] = useState<number>();
    const [leaveAcceptButtonClicked, setleaveAcceptButtonClicked] = useState<number>()
    const [leaveRejectButtonClicked, setleaveRejectedButtonClicked] = useState<number>()

    const [monthDropDownData, setMonthDropDownData] = useState([])




    const actionButtonClicked = (isVisible: boolean) => {
        setActionPopupVisibility(!isVisible);
    };

    useEffect(() => {
        fetchMonth();
        handelChangeStatus('Pending', '0')
    }, []);

    useEffect(() => {
        if (month.value && month.value !== -1 && status.value) {
            fetchData()
        }
    }, [month.value, status.value])



    const handelChangeStatus = (lable: string, value: string) => {
        setStatus({ lable: lable, value: value })

    }

    const handelChangeMonth = (lable: string, value: number) => {
        setMonth({ lable: lable, value: value })

    }


    const fetchMonth = () => {
        setFetching(true);
        httpGet(apiConstant.LEAVE_TIMESHEET)
            .then((response: any) => {
                const data = JSON.parse(response)?.data

                const monthYear = data?.map((item: any) => {
                    const startDate = new Date(item.start_date);
                    const month = startDate.toLocaleString('default', { month: 'long' });
                    const year = startDate.getFullYear();
                    return { label: `${month} ${year}`, value: item.id };
                });
                if (monthYear) {
                    setMonthDropDownData(monthYear)
                    const currentDate = new Date();
                    const currentMonth = currentDate.toLocaleString('default', { month: 'long' });
                    const currentYear = currentDate.getFullYear();
                    const monthYearString = currentMonth + " " + currentYear;
                    const currentMonthYearItem = monthYear.find((item: any) => item.label === monthYearString);
                    if (currentMonthYearItem) {
                        handelChangeMonth(currentMonthYearItem, currentMonthYearItem.value);
                    } else {
                        handelChangeMonth(monthYear[0], monthYear[0].value);
                    }
                    setFetching(false);
                }
            })
            .catch((err: any) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }

    const fetchData = () => {
        setFetching(true);
        httpGet(`${apiConstant.LEAVE_UNDERLINGS}?status=${status.value}&tID=${month.value}`)
            .then((response: any) => {
                const data = JSON.parse(response);
                config.BASE_URL == 'https://dev-kuber-v2-api.tudip.uk' ?
                    setLeaveData(data.data) :
                    setLeaveData(data.data.leaveData);
                // setLeaveData(data.data);
                setFetching(false);
            })
            .catch((err: any) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const postLeaveStatus = (leaveId: number, accept: boolean) => {
        const payload = {
            leaveId,
            leaveStatus: accept
        };


        httpPost('/leaves/statusUpdate', payload)
            .then((response: any) => {
                successToast("Status updated Sucessfully");
                fetchData();
            })
            .catch((err: any) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }



    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };
    const filteredListData = leaveData && leaveData.length > 0 && leaveData.filter((item) =>
        item.name.toLowerCase().includes(searchText.toLowerCase().trim())
    );

    return (
        <>
            <DrsOnLeaveView searchText={searchText} setSearchText={setSearchText}
                monthDropDownData={monthDropDownData}
                handelChangeMonth={handelChangeMonth}
                handelChangeStatus={handelChangeStatus}
                postLeaveStatus={postLeaveStatus}
                status={status}
                month={month}
                fetching={fetching}
                setFetching={setFetching}
                errorHandlerClicked={errorHandlerClicked}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerVisibility={errorHandlerVisibility}
                isVisible={actionPopupVisibility}
                actionButtonClicked={actionButtonClicked}
                navigation={navigation}
                setActionPopupVisibility={setActionPopupVisibility}
                leavdata={filteredListData}
            />
        </>
    );
};

export default DRsOnLeaveScreenCards;
