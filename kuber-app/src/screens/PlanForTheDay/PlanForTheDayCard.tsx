import React, { useEffect, useState } from 'react';
import { BackHandler, SafeAreaView, View } from 'react-native';
import RNText from '../../component/RNText';
import styles from './style';
import { stringText } from '../../utils/constants/stringsText';
import RNButton from '../../component/RNButton';
import HTMLViewPlanForTheDay from './HTMLRenderPlanForTheDay';

const PlanForTheDayListCard: React.FC<{
    timeSpent: string;
    projectName: string;
    subject: string;
    date: string;
    otherProjectName: string;
    body: string;
}> = ({ timeSpent, projectName, subject, date, body, otherProjectName }) => {
    const dateObj = new Date(date);
    const DateDay = `${dateObj.getDate()}`;
    const DateMonth = `${dateObj.getMonth() + 1}`;
    const daysOfWeek = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday'
    ];
    const dayIndex = dateObj.getDay();
    const dayOfWeek = daysOfWeek[dayIndex];
    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        const options:Intl.DateTimeFormatOptions = {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric',
            weekday: 'long'
        };
        const formattedDate = new Intl.DateTimeFormat('en-US', options).format(
            date
        );
        const [day, datePart] = formattedDate.split(',');
        const formattedResult = `${datePart.trim()} (${day.trim()})`;

        return formattedResult;
    };
    const [PlanForTheDayPopup, setPlanForTheDayPopup] = useState<boolean>(false);
    const handlePlanForTheDayPopup = () => {
        setPlanForTheDayPopup(!PlanForTheDayPopup);
    };
    const [projectNameIfOther, setProjectName] = useState<string | null>(
        projectName
    );
    useEffect(() => {
        if (otherProjectName) {
            setProjectName(otherProjectName);
        }
    }, [projectName]);
    return (
        <SafeAreaView style={styles.container}>
            <RNButton 
            ActiveOpacity={0.6} 
            handleOnPress={handlePlanForTheDayPopup}
            >
                <View style={styles.cardView}>
                    <View style={styles.cardUpperView}>
                        <RNText style={styles.projectName}>
                            {projectNameIfOther ? projectNameIfOther : "---"}
                        </RNText>
                        <RNText style={styles.dateField}>
                            {formatDate(date) ? formatDate(date) : "---"}
                        </RNText>
                    </View>
                    <View style={styles.cardLowerView}>
                        <View>
                            <RNText style={styles.subTimeHeading}>
                                {stringText.SUBJECT}
                            </RNText>
                            <RNText style={styles.planForTheDaySubject}>
                                { subject ? subject : "---" }
                            </RNText>
                        </View>
                        <View 
                        // style={styles.lowerRightView}
                        >
                            <RNText style={[styles.subTimeHeading, {textAlign:'right'}]}>
                                {stringText.TIMESPENTMINS}
                            </RNText>
                            <RNText style={styles.planForTheDaySubject}>
                                { timeSpent ? timeSpent : "---" }
                            </RNText>
                        </View>
                    </View>
                </View>
            </RNButton>
            <HTMLViewPlanForTheDay
                isVisible={PlanForTheDayPopup}
                onConfirm={handlePlanForTheDayPopup}
                body={body}
                subject={subject}
            />
        </SafeAreaView>
    );
};

export default PlanForTheDayListCard;
