import React, { useEffect, useState } from 'react';
import { SafeAreaView, View, FlatList, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { httpGet } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';
import RNActivityIndicator from '../../component/Loader';
import { Dropdown } from 'react-native-element-dropdown';
import { color } from '../../utils/constants/color';
import RNText from '../../component/RNText';
import apiConstant from '../../utils/constants/apiConstant';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';

export type Props = {
    navigation: any;
    route: any;
};

export type PlanForTheDayType = {
    id?: number;
    time_spent: string;
    project_name: string;
    subject: string;
    PLANFORDAY_date: string;
    body: string;
    other_project_name: string;
    is_draft?: number;
    status?: string;
    type?: string;
    submission_time?: string;
};

export interface MonthData {
    id: number;
    start_date: string;
}

const PlanForTheDayScreen = (props: Props) => {
    const [fetching, setFetching] = useState<boolean>(false);
    const [userId, setUserId] = useState<number | null>(props?.route?.params?.userId);
    const [monthPlanForTheDay, setMonthPlanForTheDay] = useState<MonthData[]>([]);
    const [monthID, setMonthId] = useState();
    const [PlanForTheDayData, setPlanForTheDayData] = useState<PlanForTheDayType[]>([]);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
    const [selectedPlan, setSelectedPlan] = useState<PlanForTheDayType | null>(null);
    const [showPlanModal, setShowPlanModal] = useState(false);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        if (!(props?.route?.params?.userId)) {
            fetchUserId();
        }
        else if (userId) {
            fetchMonth();
        }
    }, []);

    useEffect(() => {
        if (userId) {
            fetchMonth();
        }
    }, [userId]);

    useEffect(() => {
        if (monthID) {
            fetchPlanForTheDays();
        }
    }, [monthID]);

    const fetchUserId = () => {
        setFetching(true);
        httpGet(apiConstant.USER_INFO)
            .then((response: any) => {
                const UserId = JSON.parse(response)?.data;
                if (UserId) {
                    setUserId(UserId?.id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const fetchMonth = () => {
        setFetching(true);
        httpGet(`${apiConstant.ATTENDANCE_TIMESHEET}?userId=${userId}`)
            .then((response: any) => {
                const PlanForTheDay = JSON.parse(response)?.data;
                if (PlanForTheDay) {
                    setMonthPlanForTheDay(PlanForTheDay);
                    setMonthId(PlanForTheDay[0].id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const fetchPlanForTheDays = () => {
        setFetching(true);
        httpGet(`${apiConstant.PlanForTheDay}?userId=${userId}&tId=${monthID}`)
            .then((response: any) => {
                const PlanForTheDayData = JSON.parse(response)?.data;
                if (PlanForTheDayData) {
                    setPlanForTheDayData(PlanForTheDayData);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const handleMonthChange = (month: any) => {
        setMonthId(month);
    };

    const formatDateWithDay = (dateString: string): string => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const dayName = daysOfWeek[date.getDay()];
        return `${month}/${day}/${year} (${dayName})`;
    };

    const formatSubmittedAt = (dateString: string): string => {
        if (!dateString) return '---';
        const date = new Date(dateString);

        const istDate = new Date(date.getTime() + (5.5 * 60 * 60 * 1000));

        let hours = istDate.getUTCHours();
        const minutes = istDate.getUTCMinutes().toString().padStart(2, '0');
        const ampm = hours >= 12 ? 'PM' : 'AM';

        hours = hours % 12;
        hours = hours ? hours : 12;
        return `${hours}:${minutes} ${ampm}`;
    };

    const getMonthName = (dateString: string): string => {
        const date = new Date(dateString);
        const monthIndex = date.getMonth();
        const year = date.getFullYear();
        const months = [
            'January',
            'February',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December'
        ];
        return `${months[monthIndex]} ${year}`;
    };

    const formatTimeSpent = (timeSpent: string | number): string => {
        if (!timeSpent || timeSpent === '---') {
            return '---';
        }
        const minutes = parseInt(timeSpent.toString());
        if (isNaN(minutes)) {
            return '---';
        }
        const hours = minutes / 60;
        const formattedHours = hours.toFixed(1);
        return `${minutes} mins (${formattedHours} hrs)`;
    };

    const extractTypeFromBody = (body: string): string => {
        if (!body) return '---';

        const mandateTypeRegex = /<td[^>]*>([^<]*(?:code review|bug fix|development|testing|documentation|meeting|other)[^<]*)<\/td>/gi;
        const matches = body.match(mandateTypeRegex);

        if (matches && matches.length > 0) {
            const content = matches[0].replace(/<td[^>]*>/, '').replace(/<\/td>/, '').trim();
            return content || '---';
        }

        const tableCellRegex = /<td[^>]*>([^<]+)<\/td>/g;
        const tableMatches = [...body.matchAll(tableCellRegex)];

        const mandateTypes = ['code review', 'bug fix', 'development', 'testing', 'documentation', 'meeting', 'other'];

        for (const match of tableMatches) {
            const cellContent = match[1].toLowerCase().trim();
            for (const type of mandateTypes) {
                if (cellContent.includes(type)) {
                    return match[1].trim();
                }
            }
        }

        return '---';
    };

    const extractTaskDescriptionFromTable = (htmlBody: string) => {
        if (!htmlBody) return '';
        
        try {
            const taskDescriptionMatch = htmlBody.match(/Task Description.*?<\/th>.*?<td[^>]*>.*?<p>(.*?)<\/p>.*?<\/td>/s);
            if (taskDescriptionMatch && taskDescriptionMatch[1]) {
                return taskDescriptionMatch[1]
                    .replace(/<[^>]*>/g, '') 
                    .replace(/&nbsp;/g, ' ') 
                    .replace(/&amp;/g, '&') 
                    .replace(/&lt;/g, '<') 
                    .replace(/&gt;/g, '>') 
                    .replace(/&quot;/g, '"') 
                    .replace(/&#39;/g, "'") 
                    .trim(); 
            }
        } catch (error) {
            console.log('Error parsing HTML table:', error);
        }
        
        return '';
    };

    const extractMultipleSectionsFromTable = (htmlBody: string) => {
        if (!htmlBody) return [];
        
        try {
            const sections = [];
            
            const rowMatches = htmlBody.match(/<tr[^>]*>.*?<\/tr>/gs);
            
            if (rowMatches) {
                rowMatches.forEach((row, index) => {
                    if (row.includes('<th>')) {
                        return;
                    }
                    
                    const tdMatches = row.match(/<td[^>]*>(.*?)<\/td>/gs);
                    
                    if (tdMatches && tdMatches.length >= 5) { 
                        const projectName = tdMatches[0].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        const mandateType = tdMatches[1].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        const taskDescription = tdMatches[2].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        const priority = tdMatches[3].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        const timeSpent = tdMatches[4].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        
                        if (projectName || mandateType || taskDescription) {
                            sections.push({
                                projectName: projectName || '---',
                                mandateType: mandateType || '---',
                                taskDescription: taskDescription || '---',
                                taskStatus: 'In Progress', 
                                priority: priority || 'High',
                                timeSpent: timeSpent || '---'
                            });
                        }
                    }
                });
            }
            
            if (sections.length === 0) {
                const taskDescription = extractTaskDescriptionFromTable(htmlBody);
                sections.push({
                    projectName: selectedPlan?.project_name || '---',
                    mandateType: extractTypeFromBody(htmlBody),
                    taskDescription: taskDescription,
                    taskStatus: 'In Progress',
                    priority: 'High',
                    timeSpent: selectedPlan?.time_spent || '---'
                });
            }
            
            return sections;
        } catch (error) {
            console.log('Error parsing HTML table sections:', error);
            const taskDescription = extractTaskDescriptionFromTable(htmlBody);
            return [{
                projectName: selectedPlan?.project_name || '---',
                mandateType: extractTypeFromBody(htmlBody),
                taskDescription: taskDescription,
                taskStatus: 'In Progress',
                priority: 'High',
                timeSpent: selectedPlan?.time_spent || '---'
            }];
        }
    };

    const openPlanModal = (plan: PlanForTheDayType) => {
        setSelectedPlan(plan);
        setShowPlanModal(true);
    };

    const closePlanModal = () => {
        setShowPlanModal(false);
        setSelectedPlan(null);
    };

    const renderPlanModal = () => {
        if (!selectedPlan) {
            return null;
        }

        console.log('Rendering modal with plan:', selectedPlan);
        const totalTimeSpent = parseInt(selectedPlan.time_spent) || 0;
        const totalHours = (totalTimeSpent / 60).toFixed(1);

        const sections = extractMultipleSectionsFromTable(selectedPlan.body || '');

        return (
            <Modal
                visible={showPlanModal}
                animationType="slide"
                transparent={true}
                onRequestClose={closePlanModal}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <View style={styles.modalTitleContainer}>
                                <RNText style={styles.modalTitle}>
                                    PLAN FOR THE DAY {formatDateWithDay(selectedPlan.PLANFORDAY_date)}
                                </RNText>
                                <RNText style={styles.modalSubtitle}>
                                    Total time spent: {totalTimeSpent} mins ({totalHours} hrs)
                                </RNText>
                            </View>
                            <TouchableOpacity 
                                style={styles.modalCloseButton}
                                onPress={closePlanModal}
                            >
                                <Ionicons name="close" size={24} color="#333" />
                            </TouchableOpacity>
                        </View>

                        <View style={styles.modalTableContainer}>
                            <View style={styles.modalTableHeader}>
                                <RNText style={styles.modalTableHeaderText}>Project Name</RNText>
                                <RNText style={styles.modalTableHeaderText}>Mandate Type</RNText>
                                <RNText style={styles.modalTableHeaderText}>Task Description</RNText>
                                <RNText style={styles.modalTableHeaderText}>Priority</RNText>
                                <RNText style={styles.modalTableHeaderText}>Time Spent (mins)</RNText>
                            </View>

                            {sections.map((section, index) => (
                                <View key={index} style={styles.modalTableRow}>
                                    <RNText style={styles.modalTableCell}>
                                        {section.projectName}
                                    </RNText>
                                    <RNText style={styles.modalTableCell}>
                                        {section.mandateType}
                                    </RNText>
                                    <RNText style={styles.modalTableCell}>
                                        {section.taskDescription}
                                    </RNText>
                                    <RNText style={styles.modalTableCell}>
                                        {section.priority}
                                    </RNText>
                                    <RNText style={styles.modalTableCell}>
                                        {section.timeSpent}
                                    </RNText>
                                </View>
                            ))}
                        </View>
                    </View>
                </View>
            </Modal>
        );
    };

    const renderPlanForTheDayItem = ({ item }: { item: PlanForTheDayType }) => {
        const type = extractTypeFromBody(item.body) || item.type || '---';
        const submittedAt = formatSubmittedAt(item.submission_time || '');

        return (
            <TouchableOpacity 
                style={styles.planForTheDayCard}
                onPress={() => {
                    openPlanModal(item);
                }}
                activeOpacity={0.7}
            >
                <View style={styles.planForTheDayCardHeader}>
                    <RNText style={styles.planForTheDayCardDate}>
                        {formatDateWithDay(item.PLANFORDAY_date)}
                    </RNText>

                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    
                    <TouchableOpacity
                        
                        onPress={(e) => {
                            e.stopPropagation();
                            if (props.navigation) {
                                props.navigation.navigate('AddPlanForTheDay', { 
                                    planId: item.id,
                                    isEditMode: true
                                });
                            }
                        }}
                    >
                        <RNText style={{marginEnd: 20}}>✏️</RNText>
                    
                    </TouchableOpacity>
                    <RNText style={{ color: item.is_draft === 1 ? 'blue' : 'green' }}>
                        {/* {item.is_draft === 1 ? '✏️' : '⏳'} */}
                        ⏳
                    </RNText>
                    </View>
                </View>

                <View style={styles.planForTheDayTableRow}>
                    <View style={styles.planForTheDayTableHeader}>
                        <RNText style={styles.planForTheDayTableHeaderText}>Subject:</RNText>
                    </View>
                    <View style={styles.planForTheDayTableContent}>
                        <RNText style={styles.planForTheDayDetail}>{item.subject || '---'}</RNText>
                    </View>
                </View>

                <View style={styles.planForTheDayTableRow}>
                    <View style={styles.planForTheDayTableHeader}>
                        <RNText style={styles.planForTheDayTableHeaderText}>Project Name:</RNText>
                    </View>
                    <View style={styles.planForTheDayTableContent}>
                        <RNText style={styles.planForTheDayDetail}>{item.project_name || '---'}</RNText>
                    </View>
                </View>

                <View style={styles.planForTheDayTableRow}>
                    <View style={styles.planForTheDayTableHeader}>
                        <RNText style={styles.planForTheDayTableHeaderText}>Type:</RNText>
                    </View>
                    <View style={styles.planForTheDayTableContent}>
                        <RNText style={styles.planForTheDayDetail}>{type}</RNText>
                    </View>
                </View>

                <View style={styles.planForTheDayTableRow}>
                    <View style={styles.planForTheDayTableHeader}>
                        <RNText style={styles.planForTheDayTableHeaderText}>Est Time:</RNText>
                    </View>
                    <View style={styles.planForTheDayTableContent}>
                        <RNText style={styles.planForTheDayDetail}>
                            {formatTimeSpent(item.time_spent)}
                        </RNText>
                    </View>
                </View>

                <View style={styles.planForTheDayTableRow}>
                    <View style={styles.planForTheDayTableHeader}>
                        <RNText style={styles.planForTheDayTableHeaderText}>Submitted At:</RNText>
                    </View>
                    <View style={styles.planForTheDayTableContent}>
                        <RNText style={styles.planForTheDayDetail}>{submittedAt}</RNText>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };

    const handleCreateNewPlan = () => {
        if (props.navigation) {
            props.navigation.navigate('AddPlanForTheDay');
        }
    };

    return (
        <SafeAreaView style={styles.mainContainer}>
            <View style={styles.searchView}>
                <Dropdown
                    placeholder={monthID ? monthID : 'Select Month'}
                    style={styles.dropDown}
                    placeholderStyle={{ color: color.BLACK }}
                    itemTextStyle={{ color: color.BLACK }}
                    selectedTextStyle={{ color: color.BLACK }}
                    data={monthPlanForTheDay.map((month: any) => ({
                        label: getMonthName(month.start_date),
                        value: month.id
                    }))}
                    value={monthID}
                    labelField="label"
                    valueField="value"
                    onChange={(value: any) => handleMonthChange(value.value)}
                />
            </View>

            {PlanForTheDayData.length > 0 && !fetching ? (
                <FlatList
                    data={PlanForTheDayData}
                    renderItem={renderPlanForTheDayItem}
                    keyExtractor={(item, index) => `${item.PLANFORDAY_date}-${index}`}
                    contentContainerStyle={{ paddingBottom: 100 }}
                    showsVerticalScrollIndicator={false}
                />
            ) : (
                !fetching && (
                    <View style={styles.noDataView}>
                        <RNText style={styles.noDataText}>{stringText.DataNotAvailable}</RNText>
                    </View>
                )
            )}

            <TouchableOpacity
                style={styles.fab}
                onPress={handleCreateNewPlan}
                activeOpacity={0.8}
            >
                <Ionicons name="add" size={24} color={color.WHITE} />
            </TouchableOpacity>

            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
            <RNActivityIndicator animating={fetching} />
            {renderPlanModal()}
        </SafeAreaView>
    );
};
const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        paddingHorizontal: 14
    },
    searchView: {
        paddingTop: 16,
        paddingBottom: 16,
        gap: 10
    },
    dropDown: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK,
        justifyContent: 'center',
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 5,
        height: 40,
        backgroundColor: color.WHITE
    },
    noDataView: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    noDataText: {
        fontSize: 15,
        fontWeight: '400',
        color: color.BLACK,
        flexWrap: 'wrap'
    },
    planForTheDayCard: {
        backgroundColor: color.WHITE,
        borderRadius: 10,
        padding: 16,
        marginBottom: 12,
        marginHorizontal: 4,
        shadowColor: '#000',
        shadowOpacity: 0.1,
        shadowOffset: { width: 0, height: 1 },
        shadowRadius: 4,
        elevation: 3,
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    planForTheDayCardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 12,
        paddingBottom: 8,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    planForTheDayCardDate: {
        fontWeight: 'bold',
        fontSize: 16,
        color: color.BLACK,
    },

    planForTheDayTableRow: {
        flexDirection: 'row',
        marginBottom: 8,
        alignItems: 'center',
    },
    planForTheDayTableHeader: {
        width: '35%',
        paddingRight: 8,
    },
    planForTheDayTableHeaderText: {
        fontSize: 12,
        fontWeight: '600',
        color: color.ACCENT_BLUE,

    },
    planForTheDayTableContent: {
        flex: 1,
    },
    planForTheDayDetail: {
        fontSize: 12,
        color: '#333',
        fontWeight: '400',
    },
    planForTheDayIconRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        marginTop: 12,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
    },
    fab: {
        position: 'absolute',
        bottom: 20,
        right: 20,
        width: 56,
        height: 56,
        borderRadius: 28,
        backgroundColor: color.ACCENT_BLUE,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
    },
    parentCard: {
        margin: 10,
        alignSelf: 'center',
        flexDirection: 'row',
        borderRadius: 10,
        elevation: 5
    },
    dateBlock: {
        width: '20%',
        backgroundColor: color.ACCENT_BLUE,
        alignItems: 'center',
        justifyContent: 'center',
        rowGap: -5,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10
    },
    titleBlock: {
        width: '75%',
        padding: 10,
        backgroundColor: color.WHITE,
        justifyContent: 'center',
        paddingLeft: 10,
        borderTopRightRadius: 10,
        borderBottomRightRadius: 10
    },
    dayText: {
        fontSize: 22,
        color: color.WHITE,
        fontWeight: '500'
    },
    monthText: {
        fontSize: 18,
        color: color.WHITE,
        fontWeight: '500'
    },
    titleText: {
        fontSize: 20,
        color: color.ACCENT_BLUE,
        fontWeight: '800'
    },
    weekDayText: {
        fontSize: 16,
        color: color.ACCENT_BLUE,
        fontWeight: '400'
    },
    container: {
        flex: 1,
        justifyContent: 'center',
        backgroundColor: color.BACKGROUND_COLOR,
        paddingVertical: 8
    },
    cardView: {
        width: '100%',
        backgroundColor: color.WHITE,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: color.ACCENT_BLUE
    },
    cardUpperView: {
        flexDirection: 'row',
        backgroundColor: color.ACCENT_BLUE,
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 15,
        paddingVertical: 6
    },
    projectName: {
        color: color.WHITE,
        fontSize: 17,
        fontWeight: '500',
        width: '60%',
        paddingBottom: 4,
        textAlign: 'left'
    },
    dateField: {
        color: color.WHITE,
        fontSize: 16,
        fontWeight: '500',
        width: '40%',
        textAlign: 'right',
        paddingBottom: 4,
    },
    cardLowerView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 15,
        paddingVertical: 6
    },
    lowerRightView: {
        marginRight: 10
    },
    subTimeHeading: {
        fontSize: 13,
        fontWeight: '400',
        color: color.BLACK
    },
    planForTheDaySubject: {
        color: color.BLACK,
        fontSize: 15,
        fontWeight: '500'
    },

    planForTheDayParentView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.6)'
    },
    planForTheDayInnerView: {
        maxHeight: '90%',
        width: '96%',
        alignSelf: 'center',
        marginVertical: '5%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: color.WHITE,
        borderWidth: 2,
        borderColor: color.ACCENT_BLUE,
        borderRadius: 10,
        paddingTop: 12,
        paddingHorizontal: 10,
        paddingBottom: 20,
    },
    crossBtn: {
        // justifyContent:'flex-end',
        // alignSelf: 'flex-end'
    },
    scrollContainer: {
        width: '100%',
        flexGrow: 1
    },
    subjectConatiner: {
        width: '100%',
        marginBottom: 6,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    subjectTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: color.BLACK
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 10,
        width: '95%',
        maxHeight: '80%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalHeader: {
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    modalTitleContainer: {
        flex: 1,
    },
    modalTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 5,
    },
    modalSubtitle: {
        fontSize: 12,
        color: '#666',
    },
    modalCloseButton: {
        padding: 5,
    },
    modalTableContainer: {
        padding: 10,
    },
    modalTableHeader: {
        flexDirection: 'row',
        backgroundColor: '#f5f5f5',
        paddingVertical: 8,
        paddingHorizontal: 5,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    modalTableHeaderText: {
        fontSize: 10,
        fontWeight: 'bold',
        color: '#333',
        flex: 1,
        textAlign: 'center',
        paddingHorizontal: 2,
    },
    modalTableRow: {
        flexDirection: 'row',
        paddingVertical: 8,
        paddingHorizontal: 5,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
        backgroundColor: 'white',
    },
    modalTableCell: {
        fontSize: 10,
        color: '#333',
        flex: 1,
        textAlign: 'center',
        paddingHorizontal: 2,
    },
});
export default PlanForTheDayScreen;