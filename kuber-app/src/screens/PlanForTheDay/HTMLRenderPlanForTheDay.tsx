import React from 'react';
import RenderHTML from 'react-native-render-html';
import {
    View,
    Modal,
    useWindowDimensions,
} from 'react-native';
import { color } from '../../utils/constants/color';
import { ScrollView } from 'react-native-gesture-handler';
import Ionicons from 'react-native-vector-icons/Ionicons';
import styles from './style';
import RNButton from '../../component/RNButton';
import RNText from '../../component/RNText';

interface Props {
    isVisible: boolean;
    onConfirm: () => void;
    body: string;
    subject: string;
}

const HTMLViewPlanForTheDay: React.FC<Props> = ({
    body,
    isVisible,
    subject,
    onConfirm
}) => {
    const toggleModal = () => {
        onConfirm();
    };
    const { width } = useWindowDimensions();
    const htmlHeading = `<div><h3>${subject}</h3></div>`;
    const modifiedHTML = updateColumnWidths(body);

    function updateColumnWidths(html: string): string {
        return html.replace(/<tr>([\s\S]*?)<\/tr>/g, (rowMatch) => {
            let colIndex = 0;

            return rowMatch.replace(/<(td|th)([^>]*)>/g, (match, tag, attrs) => {
                colIndex += 1;
                const width = colIndex === 3 ? 350 : 120;
                if (/style=/.test(attrs)) {
                    return `<${tag}${attrs.replace(/style="(.*?)"/, `style="width: ${width}px; $1"`)}>`;
                } else {
                    return `<${tag} style="width: ${width}px;"${attrs}>`;
                }
            });
        });
    }

    return (
        <Modal
            onRequestClose={() => {
                toggleModal();
            }}
            transparent={true}
            visible={isVisible}
        >

            <View
                style={styles.planForTheDayParentView}
            >

                <View
                    style={styles.planForTheDayInnerView}
                >
                    <ScrollView
                        style={styles.scrollContainer}
                    >
                        <View style={styles.subjectConatiner}>
                            <RNText style={[styles.subjectTitle, { textAlign: 'center' }]}>
                                {subject}
                            </RNText>
                            <RNButton
                                handleOnPress={toggleModal}
                                style={styles.crossBtn}
                            >
                                <Ionicons name="close-outline" size={28} color="black" />
                            </RNButton>
                        </View>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                            <RenderHTML
                                contentWidth={1000}
                                source={{ html: modifiedHTML }}
                                tagsStyles={{
                                    table: {
                                        width: '100%',
                                        borderWidth: 1,
                                        borderColor: '#dddddd',
                                    },
                                    thead: {
                                        width: '100%',
                                        backgroundColor: '#f2f2f2'
                                    },
                                    tr: {
                                        flexDirection: 'row',
                                        width: '100%',
                                    },
                                    th: {
                                        borderWidth: 1,
                                        borderColor: '#dddddd',
                                        padding: 8,
                                        backgroundColor: '#f2f2f2',
                                        width: 150,
                                        color: color.BLACK,
                                    },
                                    td: {
                                        borderWidth: 1,
                                        borderColor: '#dddddd',
                                        padding: 8,
                                        width: 150,
                                        color: color.BLACK,
                                    },
                                    ul: { color: color.BLACK },
                                    li: { color: color.BLACK },
                                    p: { color: color.BLACK },
                                    strong: { color: color.BLACK },
                                }}
                            />
                        </ScrollView>
                    </ScrollView>
                </View>

            </View>

        </Modal>
    );
};
export default HTMLViewPlanForTheDay;
