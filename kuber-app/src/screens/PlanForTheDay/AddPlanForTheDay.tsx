import React, { useState, useEffect } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity, Text, SafeAreaView, ScrollView, Alert } from 'react-native';
import RNText from '../../component/RNText';
import MaterialTextField from '../../component/MaterialTextField';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { Dropdown } from 'react-native-element-dropdown';
import { httpGet, httpPatch, httpPost } from '../../utils/http';
import apiConstant from '../../utils/constants/apiConstant';
import RNActivityIndicator from '../../component/Loader';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import { stringText } from '../../utils/constants/stringsText';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import { color } from '../../utils/constants/color';

const emptySection = () => ({
  to: '',
  cc: '',
  project: '',
  otherProject: '',
  type: '',
  timeSpent: '',
  priority: 'High',
  description: '',
});

const emptyTaskSection = () => ({
  project: '',
  otherProject: '',
  type: '',
  timeSpent: '',
  priority: 'High',
  description: '',
});

export type Props = {
  navigation: any;
  route: any;
};

const AddPlanScreen = ({ navigation, route }: Props) => {
  const [sections, setSections] = useState([emptySection()]);
  const [mandateOptions, setMandateOptions] = useState<{ label: string; value: string; id?: number }[]>([]);
  const [projectOptions, setProjectOptions] = useState<{ label: string; value: string }[]>([]);
  const [fetching, setFetching] = useState<boolean>(false);
  const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
  const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
  const [userId, setUserId] = useState<number | null>(null);


  useEffect(() => {
    if (route?.params?.isEditMode) {
      fetchPrefillData(route.params.planId);
      navigation.setOptions({
        headerTitle: () => (
          <View style={{ flex: 1, alignItems: 'center' }}>
            <RNText style={{
              fontWeight: 'bold', fontSize: 18, fontFamily: 'Poppins-Regular',
              color: color.GREY_COLOR,
            }}>
              Edit Plan For The Day
            </RNText>
          </View>
        ),
        headerTitleAlign: 'center',
      });
    }
  }, [route?.params?.isEditMode, navigation,mandateOptions]);



  useEffect(() => {
    fetchUserId();
    fetchMandateOptions();
    fetchManagerEmail();
  }, []);


  useEffect(() => {
    if (userId) {
      fetchProjectOptions();
    }
  }, [userId]);

  const errorHandlerClicked = (
    errorHandlerVisibility: boolean,
    errorHandlerMessage: string
  ) => {
    setErrorHandlerVisibility(errorHandlerVisibility);
    setErrorHandlerMessage(errorHandlerMessage);
  };

  const fetchUserId = async () => {
    try {
      const response = await httpGet(apiConstant.USER_INFO);
      const userData = JSON.parse(response)?.data;
      if (userData) {
        setUserId(userData.id);
      }
    } catch (error) {
      console.log('Error fetching user info:', error);
    }
  };
  const fetchManagerEmail = async () => {
    try {
      const response = await httpGet(apiConstant.DRs_LIST);
      const drsData = JSON.parse(response)?.data;
      if (drsData?.managerDetails?.email) {
        setSections(prev => {
          const updated = [...prev];
          updated[0] = { ...updated[0], to: drsData.managerDetails.email };
          return updated;
        });
      } else {
        setSections(prev => {
          const updated = [...prev];
          updated[0] = { ...updated[0], to: '<EMAIL>' };
          return updated;
        });
      }
    } catch (error) {
      console.log('Error fetching manager email:', error);
      setSections(prev => {
        const updated = [...prev];
        updated[0] = { ...updated[0], to: '<EMAIL>' };
        return updated;
      });
    }
  };

  const fetchProjectOptions = async () => {
    if (!userId) return;

    try {
      const response = await httpGet(`${apiConstant.PROJECTS}?userId=${userId}`);
      const parsed = JSON.parse(response);
      if (parsed?.data && Array.isArray(parsed.data)) {
        const options = [
          ...parsed.data.map((project: string) => ({ label: project, value: project })),
          { label: 'Other', value: 'Other' }
        ];
        setProjectOptions(options);
      } else {
        setProjectOptions([{ label: 'Other', value: 'Other' }]);
      }
    } catch (error) {
      console.log('Error fetching projects:', error);
      setProjectOptions([{ label: 'Other', value: 'Other' }]);
    }
  };

  const fetchMandateOptions = async () => {
    try {
      const parsed = await httpGet(`${apiConstant.MANDATE_TYPES}`);
      const response = JSON.parse(parsed);
      if (response && response.data && Array.isArray(response.data.data)) {
        const options = response.data.data.map((item: any) => ({
          label: item.mandate_name,
          value: item.mandate_name,
          id: item.id
        }));
        setMandateOptions(options);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch mandate options.');
    }
  };

  const fetchPrefillData = async (planId: number) => {
    try {
      const res = await httpGet(`${apiConstant.PLAN_FOR_THE_DAY_REPORT}?planforthedayId=${planId}`);
      const response = JSON.parse(res);
  
      if (response?.data && Array.isArray(response.data)) {
        
        const mappedSections = response.data.map((item: any) => {
          const hasOtherProject = item.otherProjectName && item.otherProjectName.trim() !== '';
          const projectValue = hasOtherProject ? 'Other' : item.projectName;
          const otherProjectValue = hasOtherProject ? item.projectName : '';    
          const mandateType = mandateOptions.find(m => m.id === item.mandateId)?.value || '';
          return {
            to: '',
            cc: '',
            project: projectValue,
            otherProject: otherProjectValue,
            type: mandateType,
            timeSpent: item.timeSpend || '',
            priority: item.priority || 'Medium',
            description: item.taskDescription?.replace(/<\/?[^>]+(>|$)/g, '') || '',
          };
        });
  
        setSections(prev => {
          const [first] = prev;
          if (mappedSections.length > 0) {
            mappedSections[0].to = first.to || '';
            mappedSections[0].cc = first.cc || '';
          }
          return mappedSections;
        });
      }
    } catch (err) {
      console.error('Error fetching plan details for edit:', err);
    }
  };
  
  
  

  const handleAddSection = () => {
    setSections([...sections, emptyTaskSection()]);
  };

  const handleRemoveSection = (index: number) => {
    if (sections.length === 1) return;
    setSections(sections.filter((_, i) => i !== index));
  };

  const clearAllFields = () => {
    setSections([emptySection()]);
  };

  const handleChange = (index: number, field: string, value: string) => {
    const updated = [...sections];
    if (field === 'timeSpent') {
      const timeValue = parseInt(value) || 0;
      if (timeValue > 240) {
        return;
      }
    }
    if (field === 'project') {
      if (value !== 'Other') {
        updated[index]['otherProject'] = '';
      }
    }
    updated[index][field] = value;
    setSections(updated);
  };

  const isFormValid = () => {
    if (!sections[0]?.to?.trim()) {
      return false;
    }


    let hasValidTask = false;
    for (let i = 0; i < sections.length; i++) {
      const section = sections[i];

      const isProjectValid = section.project && section.project.trim() !== '';
      const isTypeValid = section.type && section.type.trim() !== '';
      const isTimeSpentValid = section.timeSpent && section.timeSpent.trim() !== '';
      const isDescriptionValid = section.description && section.description.trim() !== '';

      const isOtherProjectValid = section.project === 'Other' ?
        (section.otherProject && section.otherProject.trim() !== '') : true;

      if (isProjectValid && isTypeValid && isTimeSpentValid && isDescriptionValid && isOtherProjectValid) {
        hasValidTask = true;
        break;
      }
    }

    return hasValidTask;
  };


  const validateForm = () => {
    const firstSection = sections[0];

    if (!firstSection.to.trim()) {
      errorHandlerClicked(true, 'Please enter email address in To field');
      return false;
    }

    let hasValidTask = false;
    for (let i = 0; i < sections.length; i++) {
      const section = sections[i];

      const isProjectValid = section.project && section.project.trim() !== '';
      const isTypeValid = section.type && section.type.trim() !== '';
      const isTimeSpentValid = section.timeSpent && section.timeSpent.trim() !== '';
      const isDescriptionValid = section.description && section.description.trim() !== '';


      const isOtherProjectValid = section.project === 'Other' ?
        (section.otherProject && section.otherProject.trim() !== '') : true;

      if (isProjectValid && isTypeValid && isTimeSpentValid && isDescriptionValid && isOtherProjectValid) {
        hasValidTask = true;
        break;
      }
    }

    if (!hasValidTask) {
      errorHandlerClicked(true, 'Please fill all required fields');
      return false;
    }

    return true;
  };



  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    setFetching(true);

    const requestData = {
      to: sections[0].to,
      cc: sections[0].cc,
      planfordayData: sections.map((section, index) => {
        if (index === 0 && !section.project && !section.type && !section.timeSpent && !section.description) {
          return null;
        }

        return {
          statusId: "2",
          projectName: section.project || "Other",
          otherProjectName: section.project === "Other" ? section.otherProject : "",
          timeSpend: section.timeSpent || "0",
          taskDescription: `<p>${section.description}</p>`,
          priority: section.priority || "Medium",
          mandateId: mandateOptions.find(option => option.value === section.type)?.id?.toString() || ""
        };
      }).filter(Boolean)
    };

    console.log('Submitting data:', requestData);

    if (route?.params?.isEditMode && route?.params?.planId) {

      httpPatch(`${apiConstant.PLAN_FOR_THE_DAY}?planforthedayId=${route.params.planId}`, requestData)
        .then((response: any) => {
          setFetching(false);
          navigation.navigate(navigationStringText.WorkInfo, {
            refreshData: true,
            timestamp: Date.now()
          });
          clearAllFields();
        })
        .catch((err: any) => {
          console.error('Edit submit error:', err);
          setFetching(false);

          if (err?.response?.data?.message) {
            errorHandlerClicked(true, err.response.data.message);
          } else {
            errorHandlerClicked(true, stringText.SomethingWentwrong);
          }

          setTimeout(() => {
            navigation.navigate(navigationStringText.WorkInfo, {
              refreshData: true,
              timestamp: Date.now()
            });
            clearAllFields();
          }, 1000);
        });
    } else {
      httpPost(`${apiConstant.PLAN_FOR_THE_DAY}`, requestData)
        .then((response: any) => {
          console.log('Success response got called');
          setFetching(false);
          navigation.navigate(navigationStringText.WorkInfo, {
            refreshData: true,
            timestamp: Date.now()
          });
          clearAllFields();
        })
        .catch((err: any) => {
          console.error('Submit error:', err);
          setFetching(false);

          if (err?.response?.data?.message) {
            errorHandlerClicked(true, err.response.data.message);
          } else {
            errorHandlerClicked(true, stringText.SomethingWentwrong);
          }

          setTimeout(() => {
            navigation.navigate(navigationStringText.WorkInfo, {
              refreshData: true,
              timestamp: Date.now()
            });
            clearAllFields();
          }, 1000);
        });
    }
  };
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        {sections.map((section, idx) => (
          <View key={idx} style={styles.sectionContainer}>

            {idx === 0 && (
              <View style={styles.emailSection}>
                <MaterialTextField
                  label={
                    <Text>
                      <Text>To </Text>
                      <Text style={styles.asterisk}>*</Text>
                    </Text>
                  }
                  value={section.to}
                  onChangeText={text => handleChange(idx, 'to', text)}
                  keyboardType="email-address"
                  style={styles.materialTextField}
                />
                <MaterialTextField
                  label="Cc"
                  value={section.cc}
                  onChangeText={text => handleChange(idx, 'cc', text)}
                  keyboardType="email-address"
                  style={styles.materialTextField}
                />
              </View>
            )}


            <View style={styles.taskSection}>
              <View style={styles.field}>
                <View style={styles.labelContainer}>
                  <RNText style={styles.label}>Select Project </RNText>
                  <Text style={styles.asterisk}>*</Text>
                </View>
                <Dropdown
                  style={styles.dropdown}
                  placeholder="Choose project"
                  data={projectOptions}
                  value={section.project}
                  labelField="label"
                  valueField="value"
                  placeholderStyle={styles.dropdownText}
                  selectedTextStyle={styles.dropdownText}
                  onChange={(val) => handleChange(idx, 'project', val.value)}
                />
              </View>

              {section.project === 'Other' && (
                <View style={styles.field}>
                  <View style={styles.labelContainer}>
                    <RNText style={styles.label}>Project Name </RNText>
                    <Text style={styles.asterisk}>*</Text>
                  </View>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter project name"
                    value={section.otherProject}
                    onChangeText={text => handleChange(idx, 'otherProject', text)}
                  />
                </View>
              )}

              <View style={styles.field}>
                <View style={styles.labelContainer}>
                  <RNText style={styles.label}>Select Type </RNText>
                  <Text style={styles.asterisk}>*</Text>
                </View>
                <Dropdown
                  style={styles.dropdown}
                  placeholder="Choose type"
                  data={mandateOptions}
                  value={section.type}
                  labelField="label"
                  valueField="value"
                  placeholderStyle={styles.dropdownText}
                  selectedTextStyle={styles.dropdownText}
                  onChange={(val) => handleChange(idx, 'type', val.value)}
                />
              </View>

              <View style={styles.field}>
                <View style={styles.labelContainer}>
                  <RNText style={styles.label}>Priority </RNText>
                  <Text style={styles.asterisk}>*</Text>
                </View>
                <View style={styles.priorityRow}>
                  {['High', 'Medium', 'Low'].map(p => (
                    <TouchableOpacity
                      key={p}
                      style={[
                        styles.priorityBtn,
                        section.priority === p && styles.priorityBtnSelected,
                      ]}
                      onPress={() => handleChange(idx, 'priority', p)}
                    >
                      <Text style={[
                        styles.priorityText,
                        section.priority === p && styles.priorityTextSelected,
                      ]}>{p}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.field}>
                <View style={styles.labelContainer}>
                  <RNText style={styles.label}>Time Spent (Minutes) </RNText>
                  <Text style={styles.asterisk}>*</Text>
                </View>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <TextInput
                    style={[styles.input, { flex: 1 }]}
                    value={section.timeSpent}
                    onChangeText={text => handleChange(idx, 'timeSpent', text)}
                    keyboardType="numeric"
                    placeholder="Max. 240 minutes"
                    maxLength={3}
                  />
                  {idx === sections.length - 1 && (
                    <TouchableOpacity onPress={handleAddSection} style={{ marginLeft: 8 }}>
                      <Ionicons name="add-circle" size={28} color="#388E3C" />
                    </TouchableOpacity>
                  )}
                  {sections.length > 1 && (
                    <TouchableOpacity onPress={() => handleRemoveSection(idx)} style={{ marginLeft: 8 }}>
                      <Ionicons name="close-circle" size={28} color="#D32F2F" />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              <View style={styles.field}>
                <View style={styles.labelContainer}>
                  <RNText style={styles.label}>Description </RNText>
                  <Text style={styles.asterisk}>*</Text>
                </View>
                <TextInput
                  style={styles.descriptionInput}
                  placeholder="Enter description"
                  value={section.description}
                  onChangeText={text => handleChange(idx, 'description', text)}
                  multiline
                  textAlignVertical="top"
                />
              </View>


            </View>
          </View>
        ))}

        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={styles.cancelBtn}
            onPress={() => navigation.goBack()}
            disabled={fetching}
          >
            <Text style={styles.cancelText}>CANCEL</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.submitBtn,
              (!isFormValid() || fetching) && styles.submitBtnDisabled
            ]}
            onPress={handleSubmit}
            disabled={!isFormValid() || fetching}
          >
            <Text style={[
              styles.submitText,
              (!isFormValid() || fetching) && styles.submitTextDisabled
            ]}>
              {fetching ? 'Submitting...' : 'SUBMIT'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      <ErrorHandlerPopup
        visible={errorHandlerVisibility}
        errorHandlerMessage={errorHandlerMessage}
        errorHandlerClicked={errorHandlerClicked}
      />
      <RNActivityIndicator animating={fetching} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, padding: 10, backgroundColor: 'white' },
  sectionContainer: {
    marginBottom: 30,
    borderRadius: 10,
    borderColor: '#eee',
  },
  emailSection: {
    backgroundColor: 'white',
    padding: 6,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderBottomColor: '#eee',
  },
  taskSection: {
    backgroundColor: '#f8f8f8',
    padding: 15,
    borderRadius: 10,
  },
  field: { marginBottom: 15 },
  label: { marginBottom: 6, fontWeight: '400' },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#f5f5f5',
  },
  descriptionInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    paddingTop: 12,
    backgroundColor: '#f5f5f5',
    height: 140,
    textAlignVertical: 'top',
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#f5f5f5',
    height: 50,
    justifyContent: 'center',
  },
  dropdownText: {
    color: '#333',
    fontSize: 14,
  },
  priorityRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 4,
  },

  priorityBtn: {
    borderWidth: 1,
    borderColor: '#bbb',
    borderRadius: 6,
    paddingVertical: 6,
    flex: 1,
    minWidth: 70,
    alignItems: 'center',
    paddingHorizontal: 18,
    marginRight: 10,
    backgroundColor: '#fff',
  },
  priorityBtnSelected: {
    borderColor: '#1E3A8A',
    backgroundColor: '#e3eaff',
  },
  priorityText: {
    color: '#333',
    fontWeight: '500',
  },
  priorityTextSelected: {
    color: '#1E3A8A',
    fontWeight: '700',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 14,
    marginHorizontal: 14,
  },
  cancelBtn: {
    flex: 1,
    borderColor: '#1E3A8A',
    borderWidth: 1,
    paddingVertical: 12,
    borderRadius: 24,
    marginRight: 10,
    alignItems: 'center',
  },
  cancelText: {
    color: '#1E3A8A',
    fontWeight: '600',
  },
  submitBtn: {
    flex: 1,
    backgroundColor: '#1E3A8A',
    paddingVertical: 12,
    borderRadius: 24,
    alignItems: 'center',
  },
  submitTextDisabled: {
    color: 'white',
  },
  submitBtnDisabled: {
    backgroundColor: '#1E3A8A',
    opacity: 0.5,
  },
  submitText: {
    color: '#fff',
    fontWeight: '600',
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  materialTextField: {
    marginBottom: 16,
  },
  asterisk: {
    color: '#FF0000',
    fontWeight: 'bold',
  },
});

export default AddPlanScreen;