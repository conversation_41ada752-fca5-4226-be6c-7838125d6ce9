import React, { useEffect, useState } from "react";
import { Linking, SafeAreaView, View } from "react-native";
import RNText from "../../component/RNText";
import { httpGet } from "../../utils/http";
import { stringText } from "../../utils/constants/stringsText";
import styles from "./styles";
import RNActivityIndicator from "../../component/Loader";
import WebView from 'react-native-webview';
import { color } from "../../utils/constants/color";
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import RNButton from "../../component/RNButton";
import apiConstant from "../../utils/constants/apiConstant";

export type Props = {
    navigation: any;
    route: any;
};
export type documentType = {
    designation: string;
    email: string;
    employeeId: string;
    employeeName: string;
    imagePath: string;
    phone: string;
    userGoalInfo: string;
    userId: number;
    workStation: string;
};
export interface MonthData {
    id: number;
    start_date: string;
}

const DocumentScreen: React.FC<Props> = ({
    navigation,
    route
}) => {
    const [userId, setUserId] = useState<any>(route?.params?.userId);
    const [document, setDocument] = useState<string | null>(null);
    const [monthID, setMonthId] = useState();
    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);

    const errorHandlerClicked = (
        visibility: boolean,
        message: string
    ) => {
        setErrorHandlerVisibility(visibility);
        setErrorHandlerMessage(message);
    };

    useEffect(() => {
        if (userId) {
            fetchMonth()
        }
    }, []);
    useEffect(() => {
        if (monthID) {
            fetchDocument();
        }
    }, [monthID]);

    const fetchMonth = () => {
        setFetching(true);
        httpGet(`${apiConstant.ATTENDANCE_TIMESHEET}?userId=${userId}`)
            .then((response: any) => {
                const timeSheet = JSON.parse(response)?.data;
                if (timeSheet) {
                    setMonthId(timeSheet[0].id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };
    const fetchDocument = () => {
        setFetching(true);
        httpGet(`${apiConstant.MANAGER_VIEW}?userId=${userId}&tID=${monthID}`)
            .then((response: any) => {
                const Document = JSON.parse(response)?.data;
                setDocument(Document?.userDetails?.userGoalInfo);
                setFetching(false);
            })
            .catch((err: any) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    return (
        <SafeAreaView style={styles.parentContainer}>

            <RNButton
                style={[styles.newTabBtn, { opacity: document ? 1 : 0.5 }]}
                handleOnPress={() => {
                    if (document) {
                        Linking.openURL(`${document}`);
                    }
                }}
                ActiveOpacity={0.8}
            >
                <RNText style={styles.newTabText}>{stringText.OpenInNewTab}</RNText>
                <MaterialCommunityIcons
                            name="file-export-outline"
                            color={color.WHITE}
                            size={24}
                        />
            </RNButton>
            <View style={styles.webviewContainer}>
                {
                    document &&
                    !fetching &&
                    <WebView
                        source={{ uri: `${document}` }}
                        style={styles.webView}
                    />
                }
            </View>
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>

    );
};

export default DocumentScreen;
