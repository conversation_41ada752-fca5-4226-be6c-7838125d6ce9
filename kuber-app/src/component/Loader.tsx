import React from "react";
import { ActivityIndicator, View } from 'react-native'
import { color } from "../utils/constants/color";
import styles from "./Styles";

export type Props = {
    animating: boolean
}

const RNActivityIndicator = (props: any) => {

    const { animating } = props;

    return (
        <>
            {animating
                &&
                <View style={styles.activityIndicatorContainer}>
                    <ActivityIndicator
                        animating={animating}
                        size='large'
                        color={color.DARK_BLUE}
                        style={animating ? styles.spinnerStyle : styles.spinnerStyle1}

                    />
                </View>
            }
        </>
    )
}

export default RNActivityIndicator


