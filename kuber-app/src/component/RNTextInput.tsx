import React from 'react';
import { TextInput, KeyboardTypeOptions } from 'react-native';
import styles from './Styles';
import { color } from '../utils/constants/color';
import { TextInputProps } from 'react-native-paper';

export type Props = {
  style?: any,
  numberOfLines?: number,
  isMultiline?: boolean,
  placeHolder: string,
  inputValue: string,
  editable?: boolean,
  handelOnChange: (value: string) => void,
  keyboardType?: KeyboardTypeOptions
}

const RNTextInput: React.FC<Props> = (props) => {
  const {
    numberOfLines,
    isMultiline,
    placeHolder,
    inputValue,
    handelOnChange,
    editable,
    keyboardType,
    style
  } = props;

  return (
    <TextInput
      editable={editable}
      keyboardType={keyboardType}
      placeholder={placeHolder}
      value={inputValue}
      onChangeText={handelOnChange}
      multiline={isMultiline}
      style={[styles.inputWrapper, style]}
      {...props}
    />
  );
};

RNTextInput.defaultProps = {
  editable: true,
  keyboardType: 'default'
}

export default RNTextInput;
