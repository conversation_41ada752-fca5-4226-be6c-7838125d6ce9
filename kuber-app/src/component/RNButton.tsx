import React from 'react';
import { TouchableOpacity } from 'react-native';

export type Props = {
  children: any;
  style?: any;
  numberOfLines?: number;
  handleOnPress?: () => void;
  ActiveOpacity?: number | undefined;
  disabled?: boolean;
}

const RNButton: React.FC<Props> = (props) => {
  const {
    children,
    numberOfLines,
    style,
    handleOnPress,
    ActiveOpacity,
    disabled
  } = props;

  return (
    <TouchableOpacity
      onPress={handleOnPress}
      style={style}
      activeOpacity={ActiveOpacity}
      disabled={disabled}
      {...props}
    >
      {children}
    </TouchableOpacity>
  );
};

export default RNButton;
