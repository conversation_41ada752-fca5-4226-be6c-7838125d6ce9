import React from 'react';
import { Text } from 'react-native';
import { color } from '../utils/constants/color';

export type Props = {
  children: any,
  style?: any,
  numberOfLines?: number,
  onPress?: () => void,
}

const RNText: React.FC<Props> = (props) => {
  const {
    children,
    numberOfLines,
    style,
    onPress
  } = props;

  return (
    <Text numberOfLines={numberOfLines} {...props} style={[{color:color.BLACK},style]} onPress={onPress}>
      {children}
    </Text>
  );
};

export default RNText;
