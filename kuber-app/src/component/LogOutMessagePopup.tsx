import React, { FC } from 'react';
import { Modal, View, Text } from 'react-native';
// import CardView from "react-native-cardview";
import { Card, Icon } from '@rneui/themed';
import { stringText } from '../utils/constants/stringsText';
import styles from './Styles';
import { navigationStringText } from '../utils/constants/navigationStringText';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import RNButton from './RNButton';

type Props = {
    visible?: boolean;
    removeData: any;
    navigation: any;
    setLogoutPopupVisibility: (visible: boolean) => void;
};

const LogoutPopup: FC<Props> = (props) => {

    const { visible, removeData, navigation, setLogoutPopupVisibility } = props;

    return (
        <Modal transparent={visible} visible={visible} animationType="fade">
            <View style={styles.LogOutPopup}>
                 <Card wrapperStyle={{alignItems:'center'}} containerStyle={[styles.LogOutPopUpCardView, { elevation:4,padding:0,borderRadius:4,margin:0 }]}>
                    <Text
                        style={styles.LogOutTextPopup}
                    >
                        {stringText.LogoutTextPopUp}
                    </Text>
                    <View style={styles.LogOutPopUpButtonView}>
                        <RNButton
                            handleOnPress={() => {
                                setLogoutPopupVisibility(!visible);
                                navigation.goBack(navigationStringText.Dashboard);
                            }}
                            style={styles.NoTextView}
                        >
                            <Text
                                style={styles.NoTextStyles}
                            >
                                {stringText.NoText}
                            </Text>
                        </RNButton>
                        <RNButton
                            handleOnPress={() => {
                                setLogoutPopupVisibility(!visible);
                                GoogleSignin.revokeAccess();
                                GoogleSignin.signOut();
                                removeData()
                            }}
                            style={styles.LogOutPopUpYesView}
                        >
                            <Text
                                style={styles.LogOutTextStyles}
                            >
                                {stringText.YesText}
                            </Text>
                        </RNButton>
                    </View>
                </Card>
            </View>
        </Modal>
    );
};

export default LogoutPopup;
