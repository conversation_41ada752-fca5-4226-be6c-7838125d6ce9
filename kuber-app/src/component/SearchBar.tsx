import React from 'react';
import { TextInput, View } from 'react-native';
import Ionicons from 'react-native-vector-icons/FontAwesome';
import styles from './Styles';
import { color } from '../utils/constants/color';
import { stringText } from '../utils/constants/stringsText';

export type Props = {
  handelOnChange:(query:string)=>void;
  value:string;
  placeHolder?: string;
}

const SearchBar: React.FC<Props> = (props) => {
  const {
    handelOnChange,
    value,
    placeHolder
  } = props;

  return (
    <View style={styles.SearchServiceRequestSearchBar}>
                <Ionicons name="search" color={color.BLACK} size={16} style={{paddingBottom:1}} />
                <TextInput
                    placeholder={placeHolder ? placeHolder : stringText.SearchByTitle}
                    placeholderTextColor={color.BLACK}
                    style={styles.ServiceRequestSearchText}
                    onChangeText={(e)=>handelOnChange(e)}
                    value={value}
                />
            </View>
  );
};

export default SearchBar;
