import React from 'react'
import { View, Platform } from 'react-native';
import RNImage from './RNImage';
import RNText from './RNText';
import styles from './Styles';

const RNDrawer = (props: any) => {

    const { label, source,style } = props;

    return (
        <View style={[styles.RNDrawerItemContainer,style]}>
            <RNImage style={ Platform.OS === 'android' ? { height: 20, width: 20 } : { height: 24, width: 24  }} source={source}/>
            <RNText style={styles.RNDrawerTextStyle}>{label}</RNText>
        </View>
    );
};

export default RNDrawer;