PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.71.4)
  - FBReactNativeSpec (0.71.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.71.4)
    - RCTTypeSafety (= 0.71.4)
    - React-Core (= 0.71.4)
    - React-jsi (= 0.71.4)
    - ReactCommon/turbomodule/core (= 0.71.4)
  - Flipper (0.125.0):
    - Flipper-Folly (~> 2.6)
    - Flipper-RSocket (~> 1.4)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-<PERSON>olly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.4.3):
    - Flipper-Folly (~> 2.6)
  - FlipperKit (0.125.0):
    - FlipperKit/Core (= 0.125.0)
  - FlipperKit/Core (0.125.0):
    - Flipper (~> 0.125.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.125.0):
    - Flipper (~> 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.125.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.125.0)
  - FlipperKit/FKPortForwarding (0.125.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.125.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - libevent (2.1.12)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - OpenSSL-Universal (1.1.1100)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.71.4)
  - RCTTypeSafety (0.71.4):
    - FBLazyVector (= 0.71.4)
    - RCTRequired (= 0.71.4)
    - React-Core (= 0.71.4)
  - React (0.71.4):
    - React-Core (= 0.71.4)
    - React-Core/DevSupport (= 0.71.4)
    - React-Core/RCTWebSocket (= 0.71.4)
    - React-RCTActionSheet (= 0.71.4)
    - React-RCTAnimation (= 0.71.4)
    - React-RCTBlob (= 0.71.4)
    - React-RCTImage (= 0.71.4)
    - React-RCTLinking (= 0.71.4)
    - React-RCTNetwork (= 0.71.4)
    - React-RCTSettings (= 0.71.4)
    - React-RCTText (= 0.71.4)
    - React-RCTVibration (= 0.71.4)
  - React-callinvoker (0.71.4)
  - React-Codegen (0.71.4):
    - FBReactNativeSpec
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.4)
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/CoreModulesHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/Default (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/DevSupport (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.4)
    - React-Core/RCTWebSocket (= 0.71.4)
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-jsinspector (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTBlobHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTImageHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTTextHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-Core/RCTWebSocket (0.71.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.4)
    - React-cxxreact (= 0.71.4)
    - React-jsc
    - React-jsi (= 0.71.4)
    - React-jsiexecutor (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - Yoga
  - React-CoreModules (0.71.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.4)
    - React-Codegen (= 0.71.4)
    - React-Core/CoreModulesHeaders (= 0.71.4)
    - React-jsi (= 0.71.4)
    - React-RCTBlob
    - React-RCTImage (= 0.71.4)
    - ReactCommon/turbomodule/core (= 0.71.4)
  - React-cxxreact (0.71.4):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.4)
    - React-jsi (= 0.71.4)
    - React-jsinspector (= 0.71.4)
    - React-logger (= 0.71.4)
    - React-perflogger (= 0.71.4)
    - React-runtimeexecutor (= 0.71.4)
  - React-jsc (0.71.4):
    - React-jsc/Fabric (= 0.71.4)
    - React-jsi (= 0.71.4)
  - React-jsc/Fabric (0.71.4):
    - React-jsi (= 0.71.4)
  - React-jsi (0.71.4):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.71.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.4)
    - React-jsi (= 0.71.4)
    - React-perflogger (= 0.71.4)
  - React-jsinspector (0.71.4)
  - React-logger (0.71.4):
    - glog
  - react-native-date-picker (4.4.2):
    - React-Core
  - react-native-document-picker (9.1.1):
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-ionicons (4.6.5):
    - React
  - react-native-netinfo (11.3.1):
    - React-Core
  - react-native-pager-view (6.3.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-prevent-screenshot-ios-android (1.1.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.9.0):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-version-check (3.4.7):
    - React-Core
  - react-native-webview (13.10.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - React-perflogger (0.71.4)
  - React-RCTActionSheet (0.71.4):
    - React-Core/RCTActionSheetHeaders (= 0.71.4)
  - React-RCTAnimation (0.71.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.4)
    - React-Codegen (= 0.71.4)
    - React-Core/RCTAnimationHeaders (= 0.71.4)
    - React-jsi (= 0.71.4)
    - ReactCommon/turbomodule/core (= 0.71.4)
  - React-RCTAppDelegate (0.71.4):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.71.4):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.4)
    - React-Core/RCTBlobHeaders (= 0.71.4)
    - React-Core/RCTWebSocket (= 0.71.4)
    - React-jsi (= 0.71.4)
    - React-RCTNetwork (= 0.71.4)
    - ReactCommon/turbomodule/core (= 0.71.4)
  - React-RCTImage (0.71.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.4)
    - React-Codegen (= 0.71.4)
    - React-Core/RCTImageHeaders (= 0.71.4)
    - React-jsi (= 0.71.4)
    - React-RCTNetwork (= 0.71.4)
    - ReactCommon/turbomodule/core (= 0.71.4)
  - React-RCTLinking (0.71.4):
    - React-Codegen (= 0.71.4)
    - React-Core/RCTLinkingHeaders (= 0.71.4)
    - React-jsi (= 0.71.4)
    - ReactCommon/turbomodule/core (= 0.71.4)
  - React-RCTNetwork (0.71.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.4)
    - React-Codegen (= 0.71.4)
    - React-Core/RCTNetworkHeaders (= 0.71.4)
    - React-jsi (= 0.71.4)
    - ReactCommon/turbomodule/core (= 0.71.4)
  - React-RCTSettings (0.71.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.4)
    - React-Codegen (= 0.71.4)
    - React-Core/RCTSettingsHeaders (= 0.71.4)
    - React-jsi (= 0.71.4)
    - ReactCommon/turbomodule/core (= 0.71.4)
  - React-RCTText (0.71.4):
    - React-Core/RCTTextHeaders (= 0.71.4)
  - React-RCTVibration (0.71.4):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.4)
    - React-Core/RCTVibrationHeaders (= 0.71.4)
    - React-jsi (= 0.71.4)
    - ReactCommon/turbomodule/core (= 0.71.4)
  - React-runtimeexecutor (0.71.4):
    - React-jsi (= 0.71.4)
  - ReactCommon/turbomodule/bridging (0.71.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.4)
    - React-Core (= 0.71.4)
    - React-cxxreact (= 0.71.4)
    - React-jsi (= 0.71.4)
    - React-logger (= 0.71.4)
    - React-perflogger (= 0.71.4)
  - ReactCommon/turbomodule/core (0.71.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.4)
    - React-Core (= 0.71.4)
    - React-cxxreact (= 0.71.4)
    - React-jsi (= 0.71.4)
    - React-logger (= 0.71.4)
    - React-perflogger (= 0.71.4)
  - ReactNativeGetLocation (5.0.0):
    - React-Core
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNDateTimePicker (6.7.5):
    - React-Core
  - RNDeviceInfo (10.13.2):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.16.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNGoogleSignin (13.1.0):
    - GoogleSignIn (~> 7.1)
    - React-Core
  - RNPermissions (4.1.5):
    - React-Core
  - RNReanimated (2.17.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.30.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNVectorIcons (9.2.0):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (= 0.125.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - Flipper-RSocket (= 1.4.3)
  - FlipperKit (= 0.125.0)
  - FlipperKit/Core (= 0.125.0)
  - FlipperKit/CppBridge (= 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.125.0)
  - FlipperKit/FBDefines (= 0.125.0)
  - FlipperKit/FKPortForwarding (= 0.125.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.125.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.125.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.125.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.125.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.125.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.125.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - OpenSSL-Universal (= 1.1.1100)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-ionicons (from `../node_modules/react-native-ionicons`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-prevent-screenshot-ios-android (from `../node_modules/react-native-prevent-screenshot-ios-android`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-version-check (from `../node_modules/react-native-version-check`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - ReactNativeGetLocation (from `../node_modules/react-native-get-location`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AppAuth
    - CocoaAsyncSocket
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - fmt
    - GoogleSignIn
    - GTMAppAuth
    - GTMSessionFetcher
    - libevent
    - libwebp
    - OpenSSL-Universal
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket
    - YogaKit

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-ionicons:
    :path: "../node_modules/react-native-ionicons"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-prevent-screenshot-ios-android:
    :path: "../node_modules/react-native-prevent-screenshot-ios-android"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-version-check:
    :path: "../node_modules/react-native-version-check"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeGetLocation:
    :path: "../node_modules/react-native-get-location"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 446e84642979fff0ba57f3c804c2228a473aeac2
  FBReactNativeSpec: 241709e132e3bf1526c1c4f00bc5384dd39dfba9
  Flipper: 26fc4b7382499f1281eb8cb921e5c3ad6de91fe0
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: d9d9ade67cbecf6ac10730304bf5607266dd2541
  FlipperKit: cbdee19bdd4e7f05472a66ce290f1b729ba3cb86
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  RCT-Folly: 8dc08ca5a393b48b1c523ab6220dfdcc0fe000ad
  RCTRequired: 5a024fdf458fa8c0d82fc262e76f982d4dcdecdd
  RCTTypeSafety: b6c253064466411c6810b45f66bc1e43ce0c54ba
  React: 715292db5bd46989419445a5547954b25d2090f0
  React-callinvoker: 105392d1179058585b564d35b4592fe1c46d6fba
  React-Codegen: e5555d479f09cdd71a10ca2494cfe68971ae9084
  React-Core: f1a15b4de1cb038fd24121907623e19d646a2b02
  React-CoreModules: b1c40fe963d0046f5f7cf4363b4019880833b23d
  React-cxxreact: cc6ab5f00c9186c70bd3f8b294960e3781927a92
  React-jsc: 725bd22548e9b78504fecb8eeb228c9b1b65e296
  React-jsi: 88d624747efe4f5c05c17dde78f9e9af55f18aa8
  React-jsiexecutor: b7b9277e011235f152c1b12d3c0818eca61accf6
  React-jsinspector: 1f51e775819199d3fe9410e69ee8d4c4161c7b06
  React-logger: 23dec780f302f526c6ee95d4fe05d6b3203d8f1f
  react-native-date-picker: 5ddb34db03afc527bc07777952c2ac72fa384411
  react-native-document-picker: a2e1577d26b9758ef3092e66f5d67f6e8c7454d9
  react-native-geolocation-service: 32b2c2a3b91e70ce2a8d0c684801aaeb0a07e0ec
  react-native-ionicons: fabd9b1a848e8618ef8d3ad121cb584cffb5dc6b
  react-native-netinfo: 2e3c27627db7d49ba412bfab25834e679db41e21
  react-native-pager-view: f1d465727e14434ac123099764ce303a5938d516
  react-native-prevent-screenshot-ios-android: 28d798d8cc7a88b39130c81dc1995eea20ef628d
  react-native-render-html: 5afc4751f1a98621b3009432ef84c47019dcb2bd
  react-native-safe-area-context: 435f4c13ac75ceed6135382ee77d57d1a5b5b2d6
  react-native-splash-screen: 95994222cc95c236bd3cdc59fe45ed5f27969594
  react-native-version-check: c7b4269cd18781b51009fa4b5a8ad9ab5131ce6f
  react-native-webview: f283d112f55470959d53f59d6297816ffb4939a7
  React-perflogger: 0bb0522a12e058f6eb69d888bc16f40c16c4b907
  React-RCTActionSheet: bfd675a10f06a18728ea15d82082d48f228a213a
  React-RCTAnimation: 2fa220b2052ec75b733112aca39143d34546a941
  React-RCTAppDelegate: 8f7b47c9e834d18844d3af78fcb758ebfdf76395
  React-RCTBlob: b6c642da11cbe238c7fdb178dc4e805585710f97
  React-RCTImage: fec592c46edb7c12a9cde08780bdb4a688416c62
  React-RCTLinking: 14eccac5d2a3b34b89dbfa29e8ef6219a153fe2d
  React-RCTNetwork: 1fbce92e772e39ca3687a2ebb854501ff6226dd7
  React-RCTSettings: 1abea36c9bb16d9979df6c4b42e2ea281b4bbcc5
  React-RCTText: 15355c41561a9f43dfd23616d0a0dd40ba05ed61
  React-RCTVibration: ad17efcfb2fa8f6bfd8ac0cf48d96668b8b28e0b
  React-runtimeexecutor: 8fa50b38df6b992c76537993a2b0553d3b088004
  ReactCommon: bcbecc30155471b77b8f1f6e9cdeea00d963b36d
  ReactNativeGetLocation: 3a73f09e3da5badb66421ec310404c57632d7ccf
  RNCAsyncStorage: aa75595c1aefa18f868452091fa0c411a516ce11
  RNDateTimePicker: 47b54bf36a41c29d75ac62a05af1b38a9a721631
  RNDeviceInfo: ff9a4419a29d343280ea19a2e381bac25cb0e146
  RNFastImage: 462a183c4b0b6b26fdfd639e1ed6ba37536c3b87
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: be422bc1b0a1deb4be02c41f0b8aea1438216d55
  RNGoogleSignin: ba93c1137f8d5cebdd39b04f493fd212ddf5ecd6
  RNPermissions: 5e1aaec8ac3900652804425e6bded887171a89d6
  RNReanimated: f9f8945aad05d259324869fb79ce30b75c6062da
  RNScreens: c531fdafe2c9a650036ce32563b15fa76922ca6b
  RNVectorIcons: 5784330be9dddb5474e8b378d5f6947996c84e55
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 79dd7410de6f8ad73a77c868d3d368843f0c93e0
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: 49945e1a6d48c30f7ffc7ca1c33bf6dd26eba8f0

COCOAPODS: 1.16.2
