<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>TudipCare</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
			<key>NSExceptionDomains</key>
			<dict>
				<key>localhost</key>
				<dict>
					<key>NSExceptionAllowsInsecureHTTPLoads</key>
					<true/>
				</dict>
			</dict>
		</dict>
		<key>NSPrivacyAccessedAPITypes</key>
		<array>
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategoryFileTimestamp</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>DDA9.1</string>
				</array>
			</dict>
			<dict>
				<key>NSPrivacyAccessedAPIType</key>
				<string>NSPrivacyAccessedAPICategoryUserDefaults</string>
				<key>NSPrivacyAccessedAPITypeReasons</key>
				<array>
					<string>CA92.1</string>
				</array>
			</dict>
		</array>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>The app requires photo library access to upload the image, document and pdf.</string>
		<key>NSAppleMusicUsageDescription</key>
		<string>This app does not require access to the music.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>The app requires location access to validate the office location.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>The app requires location access to validate the office location.</string>
		<key>UIAppFonts</key>
		<array>
			<string>AntDesign.ttf</string>
			<string>Entypo.ttf</string>
			<string>EvilIcons.ttf</string>
			<string>Feather.ttf</string>
			<string>FontAwesome.ttf</string>
			<string>FontAwesome5_Brands.ttf</string>
			<string>FontAwesome5_Regular.ttf</string>
			<string>FontAwesome5_Solid.ttf</string>
			<string>Fontisto.ttf</string>
			<string>Foundation.ttf</string>
			<string>Ionicons.ttf</string>
			<string>MaterialIcons.ttf</string>
			<string>MaterialCommunityIcons.ttf</string>
			<string>SimpleLineIcons.ttf</string>
			<string>Octicons.ttf</string>
			<string>Zocial.ttf</string>
			<string>Poppins-Black.ttf</string>
			<string>Poppins-BlackItalic.ttf</string>
			<string>Poppins-Bold.ttf</string>
			<string>Poppins-BoldItalic.ttf</string>
			<string>Poppins-ExtraBold.ttf</string>
			<string>Poppins-ExtraBoldItalic.ttf</string>
			<string>Poppins-ExtraLight.ttf</string>
			<string>Poppins-ExtraLightItalic.ttf</string>
			<string>Poppins-Italic.ttf</string>
			<string>Poppins-Light.ttf</string>
			<string>Poppins-LightItalic.ttf</string>
			<string>Poppins-Medium.ttf</string>
			<string>Poppins-MediumItalic.ttf</string>
			<string>Poppins-Regular.ttf</string>
			<string>Poppins-SemiBold.ttf</string>
			<string>Poppins-SemiBoldItalic.ttf</string>
			<string>Poppins-Thin.ttf</string>
			<string>Poppins-ThinItalic.ttf</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.tudip.kuberproject</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.741974248997-ants1rvohbj14i1aqu6qh5l1vtmqprd9</string>
				</array>
			</dict>
		</array>

		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
	</dict>
</plist>
