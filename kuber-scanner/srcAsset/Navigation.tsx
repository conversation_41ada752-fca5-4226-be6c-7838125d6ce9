import { createNavigationContainerRef, NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import Entypo from 'react-native-vector-icons/Entypo';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import { color } from '../src/utils/color';
import SuccessScreen from './AssetAssignedScreen';
import Home from './Home';
import Login from './Login';
import QRCodeScannerView from './QRCodeScanner/QRScannerView';


const Stack = createStackNavigator();
export var navigationRef = createNavigationContainerRef();

const Navigation = (props: any) => {
    const { navigation } = props;




    return (
        <NavigationContainer ref={navigationRef}>
            <Stack.Navigator initialRouteName="Login">
                <Stack.Screen name="Login" component={Login}
                    options={{
                        headerShown: false
                    }}
                />
                <Stack.Screen name="Home" component={Home}
                    options={{
                        headerShown: false
                    }}
                />
                <Stack.Screen name="QRCodeScannerView" component={QRCodeScannerView}
                    options={{
                        headerShown: false,
                        headerLeft: () => (
                            <TouchableOpacity
                                onPress={() => navigationRef.goBack()}
                                style={{ marginVertical: 10, marginLeft: 10 }}
                            >
                                <Entypo name="chevron-left" color={color.DARK_BLUE} size={24} />
                            </TouchableOpacity>
                        ),
                        // headerTitleStyle: {
                        //     fontFamily: "Poppins-Regular",
                        //     fontWeight: "700",
                        //     fontSize: 18,
                        //     color: color.GREY_COLOR,
                        // },
                        headerStyle: { backgroundColor: color.WHITE },
                        headerTitle:''
                    }}
                    


                />
                <Stack.Screen name="SuccessScreen" component={SuccessScreen}
                    options={{
                        headerShown: false
                    }}
                />

            </Stack.Navigator>
        </NavigationContainer>
    );
}

export default Navigation;