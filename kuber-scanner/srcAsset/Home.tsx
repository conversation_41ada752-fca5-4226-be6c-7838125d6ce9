import AsyncStorage from '@react-native-async-storage/async-storage';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { StackActions } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';

import {
    Image,
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import ErrorHandlerPopup from '../src/component/ErrorHandlerPopup';
import Spinner from "../src/component/Loader";
import LogoutPopup from '../src/component/LogOutMessagePopup';
import { color } from '../src/utils/color';

const Home = (props: any) => {
    const { navigation } = props;
    const [loader, setLoader] = useState(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [isLogoutPopUpVisible, setIsLogoutPopUpVisible] = useState<boolean>(false);

    const GoToScanner = () => {
        navigation.navigate("QRCodeScannerView");
    };

    const signOut = async () => {
        try {
            await GoogleSignin.revokeAccess();
            await GoogleSignin.signOut();
            await AsyncStorage.removeItem('AuthToken').finally(
                navigation.dispatch(StackActions.replace("Login"))
            );
        } catch (err) {
            errorHandlerClicked(true, "Please try again")
        };
    };
    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    return (
        <SafeAreaView style={styles.mainView}>
            <TouchableOpacity
                onPress={() => setIsLogoutPopUpVisible(true)}
                style={styles.iconContainer}>
                <FontAwesome
                    name="user-circle"
                    color={color.DARK_BLUE}
                    size={40}
                    style={styles.proImg}
                />
            </TouchableOpacity>
            <View style={styles.viewStyle}>

                <Image source={require('../src/assets/Images/logokuber.png')}
                    style={styles.imgStyles}
                />
                <View>
                    <Text style={styles.textAssetHead}>Asset Maintenance Master App</Text>
                </View>
                <TouchableOpacity style={styles.containerButton}
                    onPress={GoToScanner}
                >
                    <Text style={styles.buttonText}>Tap To Scan QR Code</Text>
                </TouchableOpacity>
                <Spinner animating={loader} />
            </View>
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
            {isLogoutPopUpVisible && (
                <View style={styles.overlay}>
                    <LogoutPopup
                        visible={isLogoutPopUpVisible}
                        setIsLogoutPopUpVisible={setIsLogoutPopUpVisible}
                        removeData={signOut}
                    />
                </View>
            )}
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    mainView: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',

    },
    viewStyle: {
        flex: 1,
        justifyContent: 'center',
    },
    signOutPopup: {
        // backgroundColor:'red',
        // width:120
        //         flex: 1,
        //   alignItems: 'center',
        //   justifyContent: 'center',
    },

    containerButton: {
        display: "flex",
        marginBottom: 10,
        marginTop: 10,
        borderWidth: 2,
        width: 200,
        height: 50,
        backgroundColor: "#193C6D",
        alignSelf: "center",
        borderColor: 'white',
        borderRadius: 15,
        justifyContent: "center",

    },
    buttonText: {
        alignContent: "center",
        alignSelf: "center",
        fontWeight: "400",
        fontSize: 14,
        fontFamily: "Poppins-Regular",
        color: 'white'

    },
    imgStyles: {
        height: 100,
        width: 230,
        alignSelf: "center",
        // top: 20,
        borderRadius: 5,
    },
    textHead: {
        color: 'black',
        height: 50,
        width: 250,
        textAlign: "center",
        fontSize: 25,
        marginTop: 100,
        alignSelf: "center",
        margin: 10,
        borderBottomColor: 'black',
        borderBottomWidth: 2,
        fontWeight: "500",
        fontFamily: "Poppins-Regular",

    },
    proImg: {
        margin: 15,
    },
    iconContainer: {
        alignSelf: 'flex-end',
    },
    textAssetHead: {
        color: 'black',
        // height: 50,
        width: 300,
        textAlign: "center",
        fontSize: 25,
        marginTop: 100,
        alignSelf: "center",
        margin: 10,
        borderBottomColor: 'black',
        borderBottomWidth: 2,
        borderTopColor: 'black',
        borderTopWidth: 2,
        fontWeight: "500",
        fontFamily: "Poppins-Regular",
        padding: 4

    },
    overlay: {
        ...StyleSheet.absoluteFillObject, // This will cover the entire screen
        backgroundColor: 'rgba(0, 0, 0, 0.5)', // Adjust the alpha (0.5 for 50% transparency)
        zIndex: 1, // Place it above other elements
        justifyContent: 'center',
        alignItems: 'center',
    }
});

export default Home;