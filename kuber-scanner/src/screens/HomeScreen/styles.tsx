import { StyleSheet } from "react-native";
import { color } from "../../utils/color";

const styles = StyleSheet.create({
    proImg: {
      margin: 5,
    },
    compoHome: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
    },
    textHead: {
      color: color.BLACK,
      height: 50,
      width: 250,
      textAlign: "center",
      fontSize: 25,
      marginTop: 100,
      alignSelf: "center",
      margin: 10,
      borderBottomColor: color.BLACK,
      borderBottomWidth: 2,
      fontWeight: "500",
      fontFamily: "Poppins-Regular",
    },
    logoImg: {
      height: 100,
      width: 230,
      alignSelf: "center",
      top: 20,
      borderRadius: 5,
    },
    textButton: {
      color: color.WHITE,
      alignContent: "center",
      alignSelf: "center",
      fontWeight: "400",
      fontSize: 14,
      fontFamily: "Poppins-Regular",
    },
    buttonView: {
      display: "flex",
      marginBottom: 10,
      borderWidth: 2,
      width: 200,
      height: 50,
      backgroundColor: color.DARK_BLUE,
      alignSelf: "center",
      borderColor: color.WHITE,
      borderRadius: 15,
      justifyContent: "center",
    },
    fontView: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      shadowOpacity: 1,
      elevation: 5,
      borderRadius: 5
    },
    histoyView: {
      margin: 10
    },
    ImgView: {
      marginTop: 50
    }
  });

  export default styles;