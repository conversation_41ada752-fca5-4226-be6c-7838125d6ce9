import React from "react";
import { SafeAreaView, Text, View } from "react-native";
import * as Animatable from 'react-native-animatable';
import QRCodeScanner from "react-native-qrcode-scanner";
import ErrorHandlerPopup from "../../component/ErrorHandlerPopup";
import OrderSuccessPopup from "../../component/OrderSuccessPopup";
import { color } from "../../utils/color";
import { stringText } from "../../utils/stringConstants";
import styles from "./QRscannerstyles";

export type Props = {
  dataConvert: any,
  errorHandlerVisibility: any,
  errorHandlerMessage: any,
  errorHandlerClicked: any,
  navigation: any,
  orderSuccessPopupVisibility: any,
  orderSuccessPopupMessage: any,
  orderSuccessPopupClicked: any,
  granted: any,
  showPopUp: boolean,
  makeSlideOutTranslation: any,
  markerWidth: any,
  markerHeight: any
}

const QRScaneerView = (props: Props) => {

  const { dataConvert, errorHandlerVisibility, errorHandlerMessage, errorHandlerClicked, navigation,
    orderSuccessPopupVisibility, orderSuccessPopupMessage, orderSuccessPopupClicked, granted, showPopUp, makeSlideOutTranslation,
    markerWidth, markerHeight } = props

  return (
    <SafeAreaView style={styles.mainView}>
      {granted ? (
        showPopUp ?
          <QRCodeScanner
            onRead={({ data }) => dataConvert(data)}
            reactivate={true}
            reactivateTimeout={3000}
            cameraType="back"
            // topContent={
            //   <View>
            //     <Text style={styles.topText}>{stringText.ScanTo}</Text>
            //   </View>
            // }
            showMarker={true}
            // markerStyle={styles.marker}
            customMarker={
              <>
                <View style={[styles.containerMarker, { width: markerWidth, height: markerHeight }]}>
                  <View style={styles.cornerTopLeft} />
                  <View style={styles.cornerTopRight} />
                  
                </View>
                <Animatable.View
                  animation={makeSlideOutTranslation(
                    "translateY",
                  )}
                  direction="alternate-reverse"
                  easing="linear"
                  duration={1000}
                  iterationCount="infinite"
                  style={[styles.marker, { width: markerWidth - 35 }]}
                >
                </Animatable.View>
                <View style={[styles.containerMarker1, { width: markerWidth, height: markerHeight }]}>
                  <View style={styles.cornerBottomLeft} />
                  <View style={styles.cornerBottomRight} />
                </View>
              </>
            }
            // topViewStyle={styles.topView}
            // bottomViewStyle={styles.bottomView}
            // containerStyle={styles.containerStyle}
            cameraTimeout={100000}
            cameraTimeoutView={
              <View style={styles.cameraTimeoutView}>
                <Text style={styles.cameraTimeoutText}>Tap here scan</Text>
              </View>
            }
          />
          : <View />
      ) : (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Text>{stringText.NoCameraPermission}</Text>
        </View>

      )
      }
      <ErrorHandlerPopup
        visible={errorHandlerVisibility}
        errorHandlerMessage={errorHandlerMessage}
        errorHandlerClicked={errorHandlerClicked}
      />
      <OrderSuccessPopup
        navigation={navigation}
        visible={orderSuccessPopupVisibility}
        orderSuccessMessage={orderSuccessPopupMessage}
        orderSuccessPopupClicked={orderSuccessPopupClicked}

      />
    </SafeAreaView>
  );
};

export default QRScaneerView;