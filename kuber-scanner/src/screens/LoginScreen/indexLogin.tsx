import AsyncStorage from "@react-native-async-storage/async-storage";
import React, { useEffect, useState } from "react";
import { StackActions } from "@react-navigation/native";
import { httpGet, httpPost } from "../../utils/http";
import validate from "../../utils/validation";
import LoginScreenView from "./LoginView";
import { stringText } from "../../utils/stringConstants";
import { View, Image, TouchableOpacity, Text, StyleSheet } from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import ErrorHandlerPopup from "../../component/ErrorHandlerPopup";
// import styles from "./LoginStyles";
import Spinner from "../../component/Loader";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
const LoginScreen = (props: any) => {
  const [errorMsg, setErrorMsg] = useState(null);
  const { navigation } = props;
  const [userName, setUserName] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [passwordErrorMessage, setPasswordErrorMessage] = useState<String>("");
  const [loader, setLoader] = useState<boolean>(false);
  const [emailError, setEmailError] = useState<string>("");
  const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>("");
  const [errorHandlerVisibility, setErrorHandlerVisibility] =
    useState<boolean>(false);
  const [showPassword, setShowPassword] = useState(false);
  useEffect(() => {
    GoogleSignin.configure();
    stayLogin();
  }, []);
  const stayLogin = async () => {
    setLoader(true);
    const usertoken = await AsyncStorage.getItem("AuthToken");
    if (usertoken !== null) {
      setLoader(false);
      navigation.dispatch(StackActions.replace("Home"));
    } else {
      setLoader(false);
    }
  };

  useEffect(() => {
    stayLogin();
  }, []);

  const errorHandlerClicked = (
    errorHandlerVisibility: boolean,
    errorHandlerMessage: string
  ) => {
    setErrorHandlerVisibility(errorHandlerVisibility);
    setErrorHandlerMessage(errorHandlerMessage);
  };

  const loggedIn = () => {
    httpGet("/users/user")
      .then(async (response: any) => {
        const FoodVendorName =
          (await JSON.parse(response)?.data?.first_name) +
          " " +
          JSON.parse(response)?.data?.last_name;
        if (FoodVendorName) {
          AsyncStorage.setItem("FoodVendorName", FoodVendorName)
            .then(() => {
              setLoader(false);
              navigation.dispatch(StackActions.replace("Home"));
            })
            .catch((err) => {
              setLoader(false);
            });
        }
      })
      .catch((err) => {
        if (
          err?.response?.data?.message !== undefined &&
          err?.response?.data?.message !== null &&
          err?.response?.data?.message !== ""
        ) {
          errorHandlerClicked(true, err?.response?.data?.message);
        } else {
          errorHandlerClicked(true, `${stringText.SomethingWentwrong}`);
        }
      });
  };

  const handleGoogleSignIn = async () => {
    try {
      await GoogleSignin.signOut();
      const userInfo = await GoogleSignin.signIn();
      const googleCredential = await GoogleSignin.getTokens();
         console.log("GoogleCreds: ",googleCredential);
      if (userInfo && googleCredential) {
        const tudipEmailRegex = /@tudip\.com$/;
        console.log(userInfo);
        const email = userInfo.user.email;
        if (tudipEmailRegex.test(email)) {
          UserTokenAPI(googleCredential.accessToken);
        } else {
          GoogleSignin.revokeAccess();
          GoogleSignin.signOut();
          errorHandlerClicked(true, "Please use tudip email only");
        }
      }
    } catch (error) {
       console.log("Exception: ",error);
      setErrorMsg(JSON.stringify(error));
    }
  };
  const UserTokenAPI = (token: any) => {
    httpPost(`google-authentication`,{token} 
    
    // { token,"isScanner":true }
  )
      .then(async (response: any) => {
        const responseData = JSON.parse(response).data;
        await AsyncStorage.setItem("AuthToken", responseData.access_token);
        loggedIn();
      })
      .catch((err: any) => {
        GoogleSignin.revokeAccess();
        GoogleSignin.signOut();
        errorHandlerClicked(true, `${err.response.data.message}`);
        //   AsyncStorage.clear();
      });
  };
  return (
    // <LoginScreenView
    // emailError = {emailError}
    // passwordErrorMessage = {passwordErrorMessage}
    // getUserName ={getUserName}
    // getPassWord = {getPassWord}
    // loggedIn = {loggedIn}
    // loader = {loader}
    // errorHandlerVisibility = {errorHandlerVisibility}
    // errorHandlerMessage = {errorHandlerMessage}
    // errorHandlerClicked ={errorHandlerClicked}
    // showPassword={showPassword}
    // toggleShowPassword={toggleShowPassword}
    // />
    <View style={styles.googleButtonView}>
      <View style={styles.kuberImage}>
        <Image
          source={require("../../assets/Images/logokuber.png")}
          resizeMode="contain"
          style={styles.imageView}
        />
      </View>
      <TouchableOpacity
        style={styles.googleButton}
        onPress={() => {
          // navigation.navigate('Home')
          handleGoogleSignIn();
          // isAuthenticated ? navigation.navigate('Home') : navigation.navigate('GoogleLoginButton')
          // Handle Google Sign-In here
        }}
      >
        <View style={styles.iconContainer}>
          <MaterialCommunityIcons
            name="google"
            color="#fff" // White color for the Google icon
            size={30}
          />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.buttonText}>Login with Tudip Gmail</Text>
        </View>
      </TouchableOpacity>
      <Spinner animating={loader} />
      <ErrorHandlerPopup
        visible={errorHandlerVisibility}
        errorHandlerMessage={errorHandlerMessage}
        errorHandlerClicked={errorHandlerClicked}
      />
    </View>
  );
};
const styles = StyleSheet.create({
  googleButtonView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: 200,
    height: 50,
    alignSelf: "center",
  },
  googleButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#193c6c", // Google red
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    padding: 20,
    marginTop: 20,
  },
  iconContainer: {
    marginRight: 15,
  },
  textContainer: {
    flex: 1,
  },
  buttonText: {
    color: "#fff", // White color for text
    fontSize: 18,
  },
  imageView: {
    height: 65,
    width: 166,
    // alignSelf: "center",
    marginTop: 90,
  },
  kuberImage: {
    marginBottom: 80,
  },
});
export default LoginScreen;
