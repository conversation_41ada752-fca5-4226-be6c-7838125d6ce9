import { StyleSheet } from "react-native";
import { color } from "../../utils/color";

const styles = StyleSheet.create({
  mainView: {
    backgroundColor: color.WHITE,
    flex: 1,
  },
  containerView: {
    flexDirection: "row",
  },
  calenderView: {
    flex: 1,
    alignSelf: "center",
    justifyContent: "center",
    padding: 10,
    height: 18,
    width: 20,
    resizeMode: "contain",
  },
  inputText: {
    borderWidth: 1,
    borderColor: color.DARK_BLUE,
    width: 99,
    height: 39,
    borderRadius: 5,
    backgroundColor: color.WHITE,
    fontWeight: "400",
    fontSize: 12,
    color: color.GREY_COLOR,
    fontFamily: "Poppins-Regular",
  },
  TextImg: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  ButtonImg: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  TextImg1: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  textOrder: {
    margin: 16,
    color: color.GREY_COLOR,
    fontSize: 14,
    fontWeight: "600",
    fontFamily: "Poppins-Regular",
  },
  tableView: {
    margin: 20,
    backgroundColor: color.WHITE,
    borderWidth: 1,
  },
  ImgView: {
    padding: 10,
    borderWidth: 1,
    borderColor: color.DARK_BLUE,
    marginLeft: 5,
    height: 39,
    width: 39,
    borderRadius: 5,
  },
  textK: {
    color: color.GREY_COLOR,
    fontSize: 14,
    fontWeight: "400",
    fontFamily: "Poppins-Regular",
  },
  textHead: {
    fontWeight: "400",
    fontSize: 12,
    color: color.GREY_COLOR,
    textAlign: "center",
  },
  rowStyle: {
    borderBottomWidth: 1,
    borderColor: color.DARK_RED,
  },
  searchButton: {
    display: "flex",
    marginBottom: 10,
    borderWidth: 2,
    width: 200,
    height: 50,
    backgroundColor: color.DARK_BLUE,
    alignSelf: "center",
    borderColor: color.WHITE,
    borderRadius: 15,
    justifyContent: "center",
  },
  searchText: {
    color: color.WHITE,
    alignContent: "center",
    alignSelf: "center",
  },
  headerView: {
    backgroundColor: color.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: color.BLACK,
  },
  headerLunch: {
    fontWeight: "400",
    fontSize: 12,
    color: color.GREY_COLOR,
    textAlign: "center",
  },
  head: {
    height: 44,
    backgroundColor: color.WHITE,
  },
  headText: {
    fontWeight: "900",
    fontSize: 14,
    color: color.GREY_COLOR,
    textAlign: "center",
    fontFamily: "Poppins-Regular",
  },
  text: {
    fontWeight: "400",
    fontSize: 12,
    color: color.GREY_COLOR,
    textAlign: "center",
    fontFamily: "Poppins-Regular",
    margin: 12,
  },
  noDataView: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: -10000
  }
});

export default styles;