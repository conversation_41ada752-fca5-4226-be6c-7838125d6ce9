import React, { FC, Fragment, useEffect, useRef, useState } from "react";
import { Modal, View, Text, TouchableOpacity } from "react-native";
import { FlatList, TextInput } from "react-native-gesture-handler";
import { color } from "../utils/color";
import { httpGet, httpPost } from "../../srcAsset/utils/http";
// import { addDoc, collection, db } from "../utils/config";
import { stringText } from "../utils/stringConstants";
// import firestore from '@react-native-firebase/firestore';
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import SearchableDropdown from 'react-native-searchable-dropdown';
import { Image } from "react-native-animatable";
import ErrorHandlerPopup from "./ErrorHandlerPopup";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Card } from "@rneui/base";


type Props = {
  visible?: boolean;
  orderSuccessMessage: string;
  successPopupClicked: (isVisible: boolean, message: string) => void;
  navigation: any;
  isAssigned: boolean;
  empAssignedShowText: any;
  showModal: boolean;
  setShowModal: any;
  assignStatus: string;
  closeModal: any;
  setIsAssigneLogin: any;
  isassignedlogin: any;
  scannedData: any;
};

const SuccessPopup: FC<Props> = (props) => {
  const { visible, orderSuccessMessage, successPopupClicked, navigation, isAssigned, empAssignedShowText, setShowModal, assignStatus,
    closeModal, setIsAssigneLogin, isassignedlogin, scannedData } =
    props;

  useEffect(() => {
    EmployeeSearch();
    // console.log("weewewwe1", assignStatus);
    setLaptopId(empAssignedShowText);
    setLaptopname(scannedData);
    // if (assignStatus == 'Assign') {
    //   EmployeeSearch();
    // }

  }, [])

  const [employeeId, setEmployeeId] = useState('');
  const [laptopId, setLaptopId] = useState('');
  const [laptopname, setLaptopname] = useState('');
  const [comments, setComments] = useState('');
  const [errormsg, setErrormsg] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(true);
  const [search, setSearch] = useState('');
  const [employeeName, setEmployeeName] = useState('');
  const [reasonsChange, setReasonsChange] = useState('');
  const [clicked, setClicked] = useState(false);
  const searchRef = useRef();
  const [allEmployees, setAllEmployees] = useState([]
  )
  const [data, setData] = useState([]);
  const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
  const [errorHandlerVisibility, setErrorHandlerVisibility] =
    useState<boolean>(false);
  const [isDropdownOpen, setDropdownOpen] = useState(false);


  const onSearch = (search: any) => {
    if (search !== '') {
      const tempData = allEmployees.filter((item) => {
        return item.info.toLowerCase().indexOf(search.toLowerCase()) > -1;
      });
      setData(tempData);
    } else {
      setData(allEmployees);
    }
  };



  const handleReasonToChange = (text: any) => {
    setReasonsChange(text);
    if (reasonsChange.length > 0 &&
      comments.length > 0 || comments.length > 0 && reasonsChange.length > 0) {
      setIsSubmitDisabled(false);
    } else {
      setIsSubmitDisabled(true);
    }
  }
  const handlecommentToChange = (text: any) => {

    setComments(text);

    // console.log("reasonsChange.length", reasonsChange.length);
    // console.log("comments.length", comments.length);
    // console.log(reasonsChange.length > 0 &&
    //   comments.length > 0);

    if (reasonsChange.length > 0 &&
      comments.length > 0 || comments.length > 0 && reasonsChange.length > 0) {
      setIsSubmitDisabled(false);
    } else {
      setIsSubmitDisabled(true);
    }
  }


  const handleLaptopIdChange = (text: any) => {
    setLaptopId(text);
    setIsSubmitDisabled(text.trim().length === 0);
  };



  const handleCommentChange = (text: any) => {
    setComments(text);
    if (comments.length > 0 &&
      employeeName.length > 0) {
      setIsSubmitDisabled(false);
    } else {
      setIsSubmitDisabled(true);
    }
  };

  const handleEmployeeChange = (text: any) => {
    onSearch(text);
    setSearch(text);
    setIsSubmitDisabled(text.trim().length === 0);

  };


  const errorHandlerClicked = (
    errorHandlerVisibility: boolean,
    errorHandlerMessage: string
  ) => {
    setErrorHandlerVisibility(errorHandlerVisibility);
    setErrorHandlerMessage(errorHandlerMessage);
  };



  const AssignedAsset = () => {

    const parsedEmployeeId = parseInt(employeeId, 10);
    const parsedLaptopId = parseInt(empAssignedShowText, 10);

    const axiosConfig = {
      headers: {
        Authorization: 'Bearer YOUR_TOKEN',
        ContentType: 'application/json',
      },
    };

    const payload = {
      employee_id: parsedEmployeeId,
      laptop_id: parsedLaptopId,
      comments: comments,
      headers: {
        Authorization: `Bearer ${AsyncStorage.getItem('AuthToken')}`,
        ContentType: 'application/json',
      },
    };

    // console.log("payload===>>>",
    //   payload);

    httpPost("assign-asset", payload)
      .then(async (response: any) => {
        // console.log("response===>>", JSON.parse(response));
        if (JSON.parse(response).data.allocated) {
          setEmployeeId('');
          setLaptopId('');
          setComments('');
          setErrorMessage('');
          setErrormsg(false);

        } else {
          setErrorMessage(JSON.parse(response).data.message);
          setErrormsg(true);

        }


      })
      .catch((err: any) => {

        // console.log("err", err.message);
        setEmployeeId('');
        setLaptopId('');
        setComments('');

        if (
          err?.response?.data?.message !== undefined &&
          err?.response?.data?.message !== null &&
          err?.response?.data?.message !== ""
        ) {
          errorHandlerClicked(true, err?.response?.data?.message);
        } else {
          errorHandlerClicked(
            true,
            `${stringText.SomethingWentwrong}`
          );
        }
      });
  }

  const UnassignedAsset = () => {

    const parsedEmployeeId = parseInt(employeeId, 10);
    const parsedLaptopId = parseInt(empAssignedShowText, 10);

    const payload = {
      employee_id: parsedEmployeeId,
      laptop_id: parsedLaptopId,
      comments: comments,
      reason_for_change: reasonsChange
    };

    // console.log("payload===>>>", payload);

    httpPost("unallocate-asset", payload)
      .then(async (response: any) => {
        // console.log("response===>>", response);

        setEmployeeId('');
        setLaptopId('');
        setComments('');
      })
      .catch((err: any) => {

        // console.log("err", err.message);
        setEmployeeId('');
        setLaptopId('');
        setComments('');
        if (
          err?.response?.data?.message !== undefined &&
          err?.response?.data?.message !== null &&
          err?.response?.data?.message !== ""
        ) {
          errorHandlerClicked(true, err?.response?.data?.message);
        } else {
          errorHandlerClicked(
            true,
            `${stringText.SomethingWentwrong}`
          );
        }
      });
  }

  const EmployeeSearch = () => {
    httpGet("users/getAllEmployees")
      .then(async (response: any) => {

        const responseData = JSON.parse(response).data

        // console.log(responseData);
        setAllEmployees(responseData);
        setData(responseData);
      })
      .catch((err: any) => {
        // console.log("err", err.message);
        setEmployeeId('');
        setLaptopId('');
        setComments('');



        if (
          err?.response?.data?.message !== undefined &&
          err?.response?.data?.message !== null &&
          err?.response?.data?.message !== ""
        ) {
          errorHandlerClicked(true, err?.response?.data?.message);
        } else {
          errorHandlerClicked(
            true,
            `${stringText.SomethingWentwrong}`
          );
        }
      });
  }

  return (
    <Modal transparent={visible} visible={visible} animationType="fade">
      <View
        style={{
          backgroundColor: "rgba(0,0,0,0.75)",
          flex: 1,
          flexDirection: "column",
          justifyContent: "center",
        }}
      >
        <Card
          wrapperStyle={{
            alignSelf: "center",
            backgroundColor: color.WHITE,
            width: "80%",
            justifyContent: "center",
            // alignItems: "center",
            paddingTop: 9,
            paddingBottom: 12,
            paddingHorizontal: 9,
          }}
          containerStyle={{
            elevation: 4,
            padding: 0,
            borderRadius: 4,
            margin: 0,
          }}
        >
          <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignContent: 'flex-end', alignItems: 'flex-end' }}>
            <TouchableOpacity
              onPress={() => {
                setEmployeeId('');
                onSearch('');
                setSearch('');
                setEmployeeName('');
                setReasonsChange('');
                // Close the modal and go back to the previous screen
                // navigation.goBack();
                // setShowModal(false);
                closeModal();
                setComments('');
                setClicked(false);
                // setSearch('');
                // onSearch('');
              }}
            >
              <MaterialCommunityIcons
                name="close-circle-outline"
                color={color.DARK_BLUE}
                size={30}
              />
            </TouchableOpacity>

          </View>
          <View style={{ alignItems: 'center' }}>
            <Text style={{
              marginTop: 10,
              margin: 9,
              color: color.GREY_COLOR,
              fontWeight: "500",
              fontSize: 25,
              fontFamily: "Poppins-Regular",

            }}>{"Laptop ID: "}{scannedData}</Text>
          </View>
          <View style={{ alignItems: 'center' }}>
            {/* <Text
              style={{
                marginTop: 10,
                margin: 9,
                color: color.GREY_COLOR,
                fontWeight: "500",
                fontSize: 18,
                fontFamily: "Poppins-Regular",
                marginLeft: 20
              }}
            >
              Enter Employee Information
            </Text> */}
            {assignStatus == 'Assign' ?
              <>
                <View style={{ alignSelf: 'flex-start', marginLeft: 30, marginBottom: -3 }}>
                  <Text style={{
                    // marginBottom: 6,
                    color: color.BLACK,
                    fontWeight: "400",
                    fontSize: 12,
                    fontFamily: "Poppins-Regular",
                  }}>Employee<Text style={{ color: 'red' }}> *</Text></Text>
                </View>

                <TouchableOpacity
                  style={{
                    width: '90%',
                    height: 50,
                    borderRadius: 10,
                    borderWidth: 1,
                    alignSelf: 'center',
                    marginTop: 10,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingLeft: 15,
                    paddingRight: 15,
                  }}
                  onPress={() => {
                    setClicked(!clicked);
                  }}>
                  <Text style={{
                    fontWeight: "400",
                    fontSize: 14,
                    fontFamily: "Poppins-Regular",
                    color: color.BLACK,
                    textAlign: 'center'
                  }}>
                    {employeeName == '' ? 'Select Employee...' : employeeName}
                  </Text>

                </TouchableOpacity>
              </> : <View />}
            {
              assignStatus == 'Assign' ?
                clicked ? (
                  <View
                    style={{
                      elevation: 5,
                      marginTop: 20,
                      height: 300,
                      alignSelf: 'center',
                      width: '90%',
                      backgroundColor: '#fff',
                      borderRadius: 10,
                    }}>
                    <TextInput
                      placeholder="Search..."
                      placeholderTextColor={color.GREAYSCALE}
                      value={search}
                      ref={searchRef}
                      onChangeText={
                        txt => {
                          onSearch(txt);
                          setSearch(txt);
                        }}
                      style={{
                        width: '90%',
                        height: 50,
                        alignSelf: 'center',
                        borderWidth: 0.2,
                        borderColor: '#8e8e8e',
                        borderRadius: 7,
                        marginTop: 20,
                        paddingLeft: 20,
                        color: color.GREY_COLOR,
                        fontWeight: "400",
                        fontSize: 14,
                        fontFamily: "Poppins-Regular",
                      }}
                    />

                    <FlatList
                      data={data}
                      renderItem={({ item, index }) => {
                        return (
                          <TouchableOpacity
                            style={{
                              width: '85%',
                              alignSelf: 'center',
                              height: 50,
                              justifyContent: 'center',
                              borderBottomWidth: 0.5,
                              borderColor: '#8e8e8e',
                            }}
                            onPress={() => {
                              setEmployeeId(item.userId);
                              setClicked(!clicked);
                              onSearch('');
                              setSearch('');
                              setEmployeeName(item.info);
                            }}>
                            <Text style={{
                              fontWeight: "400",
                              fontSize: 14,
                              fontFamily: "Poppins-Regular",
                              color: 'black'
                            }}>{item.info}</Text>
                          </TouchableOpacity>
                        );
                      }}
                    />
                  </View>
                ) : <View /> : <View />}


            {/* <TextInput
              placeholderTextColor={'black'}
              textAlign={'center'}
              style={{
                margin: 9,
                color: color.GREY_COLOR,
                fontWeight: "400",
                fontSize: 14,
                fontFamily: "Poppins-Regular",
                borderWidth: 1,
                width: "90%",
                borderRadius: 10,
              }}
              // onChangeText={}
              value={String(empAssignedShowText)}
              defaultValue={String(empAssignedShowText)}
            >

            </TextInput> */}

            {assignStatus === 'Unassign' ? (
              <>
                <View style={{ alignSelf: 'flex-start', marginLeft: 30, marginBottom: -3, marginTop: 10 }}>
                  <Text style={{
                    // marginBottom: 6,
                    color: color.BLACK,
                    fontWeight: "400",
                    fontSize: 12,
                    fontFamily: "Poppins-Regular",
                  }}>Reason for Unallocation<Text style={{ color: 'red' }}> *</Text></Text>
                </View>
                <TouchableOpacity
                  style={{
                    width: '90%',
                    height: 50,
                    borderRadius: 10,
                    borderWidth: 1,
                    alignSelf: 'center',
                    marginTop: 10,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingLeft: 15,
                    paddingRight: 15,
                  }}
                  onPress={() => {
                    setClicked(!clicked);
                    handleReasonToChange(reasonsChange);
                  }}
                >
                  <Text
                    style={{
                      fontWeight: "400",
                      fontSize: 14,
                      fontFamily: "Poppins-Regular",
                      color: 'black',
                      textAlign: 'center',
                    }}
                  >
                    {reasonsChange === '' ? 'Reason for Change' : reasonsChange}
                  </Text>
                </TouchableOpacity>
              </>
            ) : null}

            {assignStatus === 'Unassign' && clicked ? (

              <>

                <View
                  style={{
                    elevation: 5,
                    marginTop: 20,
                    height: 200, // Adjust the height as per your requirement
                    alignSelf: 'center',
                    width: '90%',
                    backgroundColor: '#fff',
                    borderRadius: 10,
                  }}
                >
                  <TouchableOpacity
                    style={{
                      width: '85%',
                      alignSelf: 'center',
                      height: 50,
                      justifyContent: 'center',
                      borderBottomWidth: 0.5,
                      borderColor: '#8e8e8e',
                    }}
                    onPress={() => {
                      // Handle the selection of "Damaged"

                      setReasonsChange("Damaged");
                      setClicked(!clicked);
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "400",
                        fontSize: 14,
                        fontFamily: "Poppins-Regular",
                        color: 'black',
                      }}
                    >
                      Damaged
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={{
                      width: '85%',
                      alignSelf: 'center',
                      height: 50,
                      justifyContent: 'center',
                      borderBottomWidth: 0.5,
                      borderColor: '#8e8e8e',
                    }}
                    onPress={() => {
                      // Handle the selection of "Technology changed"
                      setReasonsChange("Technology changed");
                      setClicked(!clicked);
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "400",
                        fontSize: 14,
                        fontFamily: "Poppins-Regular",
                        color: 'black',
                      }}
                    >
                      Technology changed
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={{
                      width: '85%',
                      alignSelf: 'center',
                      height: 50,
                      justifyContent: 'center',
                      borderBottomWidth: 0.5,
                      borderColor: '#8e8e8e',
                    }}
                    onPress={() => {
                      // Handle the selection of "Wrong Allocation"
                      setReasonsChange("Wrong Allocation");
                      setClicked(!clicked);
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "400",
                        fontSize: 14,
                        fontFamily: "Poppins-Regular",
                        color: 'black',
                      }}
                    >
                      Wrong Allocation
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={{
                      width: '85%',
                      alignSelf: 'center',
                      height: 50,
                      justifyContent: 'center',
                      borderBottomWidth: 0.5,
                      borderColor: '#8e8e8e',
                    }}
                    onPress={() => {
                      // Handle the selection of "Relieving"
                      setReasonsChange("Relieving");
                      setClicked(!clicked);
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "400",
                        fontSize: 14,
                        fontFamily: "Poppins-Regular",
                        color: 'black',
                      }}
                    >
                      Relieving
                    </Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : null}

            {assignStatus === 'Assign' ?
              <>
                <View style={{ alignSelf: 'flex-start', marginLeft: 30, marginBottom: -3, marginTop: 10 }}>
                  <Text style={{
                    // marginBottom: 6,
                    color: color.BLACK,
                    fontWeight: "400",
                    fontSize: 12,
                    fontFamily: "Poppins-Regular",
                  }}>Comments<Text style={{ color: 'red' }}> *</Text></Text>
                </View>
                <TextInput
                  placeholder="Add comment..."
                  placeholderTextColor={'black'}
                  // textAlign={'left'}
                  textAlignVertical="top"
                  style={{
                    height: 'auto', // Set to 'auto' to allow dynamic height based on content
                    minHeight: 100,  // Minimum height
                    maxHeight: 100, 
                    margin: 9,
                    color: color.GREY_COLOR,
                    fontWeight: "400",
                    fontSize: 14,
                    fontFamily: "Poppins-Regular",
                    borderWidth: 1,
                    width: "90%",
                    minWidth:"90%",
                    maxWidth:"90%",
                    borderRadius: 10,
                    paddingLeft: 15,
                    // marginLeft:10
                  }}
                  multiline={true} 
                  numberOfLines={3}  
                  onChangeText={handleCommentChange}
                  value={comments}
                >
                </TextInput>

              </> :
              <>
                <View style={{ alignSelf: 'flex-start', marginLeft: 30, marginBottom: -3, marginTop: 10 }}>
                  <Text style={{
                    // marginBottom: 6,
                    color: color.BLACK,
                    fontWeight: "400",
                    fontSize: 12,
                    fontFamily: "Poppins-Regular",
                  }}>Comments<Text style={{ color: 'red' }}> *</Text></Text>
                </View>
                <TextInput
                  placeholder="Add comment"
                  placeholderTextColor={'black'}
                  // textAlign={'left'}
                  textAlignVertical="top"
                  style={{
                    height: 'auto', 
                    minHeight: 120,
                    maxHeight: 120, 
                    margin: 9,
                    color: color.GREY_COLOR,
                    fontWeight: "400",
                    fontSize: 14,
                    fontFamily: "Poppins-Regular",
                    borderWidth: 1,
                    width: "90%",
                    borderRadius: 10,
                    paddingLeft: 15,
                    minWidth:"90%",
                    maxWidth:"90%",
                  }}
                  numberOfLines={3}
                  multiline={true}   
                  onChangeText={handlecommentToChange}
                  value={comments}
                  returnKeyType={'default'}
                >
                </TextInput>

              </>

            }




            {errormsg &&

              <Text style={{ color: 'red', fontSize: 14, fontFamily: "Poppins-Regular" }}>{errorMessage}</Text>
            }
            <View style={{ flexDirection: "row", marginTop: 15, width: "60%", justifyContent: 'center' }}>

              <TouchableOpacity
                onPress={async () => {
                  successPopupClicked(true, "");
                  if (assignStatus == 'Assign') {
                    await AssignedAsset();
                  } else {
                    await UnassignedAsset();
                  }

                  navigation.navigate('SuccessScreen');
                }}
                style={{
                  flex: 1,
                  backgroundColor: color.DARK_BLUE,
                  justifyContent: "center",
                  alignItems: "center",
                  marginLeft: 10,
                  borderRadius: 4,
                  paddingVertical: 7,
                }}
                disabled={isSubmitDisabled}
              >
                <Text
                  style={{
                    color: color.WHITE,
                    fontWeight: "400",
                    fontSize: 13,
                    fontFamily: "Poppins-Regular",
                  }}
                >
                  Done
                </Text>
              </TouchableOpacity>

            </View>
          </View>
        </Card>
      </View>
      <ErrorHandlerPopup
        visible={errorHandlerVisibility}
        errorHandlerMessage={errorHandlerMessage}
        errorHandlerClicked={errorHandlerClicked}
      />
    </Modal>
  );
};

export default SuccessPopup;
