import React from "react";
import { TouchableOpacity } from "react-native";

export type Props = {
  children: any;
  style?: any;
  numberOfLines?: number;
  handelOnPress?: () => void;
};

const RNButton: React.FC<Props> = (props) => {
  const { children, numberOfLines, style, handelOnPress } = props;

  return (
    <TouchableOpacity onPress={handelOnPress} {...props} style={style}>
      {children}
    </TouchableOpacity>
  );
};

export default RNButton;
