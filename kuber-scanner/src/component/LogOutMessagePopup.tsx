import React, { <PERSON> } from "react";
import { Modal, View, Text, TouchableOpacity, Platform } from "react-native";
import { stringText } from "../utils/stringConstants";
import styles from "./Styles";
import { Card } from "@rneui/base";

type Props = {
    visible?: boolean;
    removeData: any;
    setIsLogoutPopUpVisible: any;
};

const LogoutPopup: FC<Props> = (props) => {

    const { visible, removeData, setIsLogoutPopUpVisible } = props;

    return (
        <Modal transparent={visible} visible={visible} animationType="fade">
            <View
                style={styles.LogOutPopup}
            >
                <Card
                    wrapperStyle={[styles.LogOutPopUpCardView, Platform.isPad && { width : '40%'}]}
                    containerStyle={{
                        elevation: 4,
                        padding: 0,
                        borderRadius: 4,
                        margin: 0,
                      }}
                >
                    <Text
                        style={styles.LogOutTextPopup}
                    >
                        {stringText.LogoutTextPopUp}
                    </Text>
                    <View style={styles.LogOutPopUpButtonView}>
                        <TouchableOpacity
                            onPress={() => {
                                setIsLogoutPopUpVisible(!visible);
                            }}
                            style={styles.NoTextView}
                        >
                            <Text
                                style={styles.NoTextStyles}
                            >
                                {stringText.NoText}
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => {
                                setIsLogoutPopUpVisible(!visible);
                                removeData()
                            }}
                            style={styles.LogOutPopUpYesView}
                        >
                            <Text
                                style={styles.LogOutTextStyles}
                            >
                                {stringText.YesText}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </Card>
            </View>
        </Modal>
    );
};

export default LogoutPopup;