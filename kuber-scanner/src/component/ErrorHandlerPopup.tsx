import React, { FC } from "react";
import { Modal, View, Text } from "react-native";
import Feather from "react-native-vector-icons/Feather";
import { color } from "../utils/color";
import { Card } from "@rneui/base";

type Props = {
  visible?: boolean;
  errorHandlerMessage: string;
  errorHandlerClicked: (
    errorHandlerVisibility: boolean,
    errorHandlerMessage: string
  ) => void;
};

const ErrorHandlerPopup: FC<Props> = (props) => {
  const { visible, errorHandlerMessage, errorHandlerClicked } = props;

  if (visible) {
    setTimeout(function () {
      errorHandlerClicked(false, "");
    }, 3000);
  }

  return (
    <Modal transparent={visible} visible={visible} animationType="fade">
      <View
        style={{
          backgroundColor: "rgba(0,0,0,0)",
          flex: 1,
          flexDirection: "column",
          justifyContent: "flex-end",
          paddingBottom: 25,
        }}
      >
        <Card
          wrapperStyle={{
            alignSelf: "center",
            backgroundColor: color.WHITE,
            width: "90%",
            justifyContent: "center",
            alignItems: "center",
            paddingTop: 9,
            paddingBottom: 12,
            paddingHorizontal: 9,
          }}
          containerStyle={{
            elevation: 4,
            padding: 0,
            borderRadius: 4,
            margin: 0,
          }}
        >
          <Feather name="alert-circle" color={color.DARK_RED} size={24} />
          <Text
            style={{
              marginTop: 9,
              color: color.GREY_COLOR,
              fontWeight: "400",
              fontSize: 12,
              fontFamily: "Poppins-Regular",
            }}
          >
            {errorHandlerMessage}
          </Text>
        </Card>
      </View>
    </Modal>
  );
};

export default ErrorHandlerPopup;
