import React, { useEffect, useState } from "react";
import { LogBox, SafeAreaView, StyleSheet, Text, View } from "react-native";
import KeepAwake from 'react-native-keep-awake';

// add import for the Tudipcare Scanner
import Navigation from "./src/navigation/Navigation";
import Spinner from './src/component/Loader';
import { addEventListener } from '@react-native-community/netinfo';
//add import for the Asset Scanner
// import Navigation from './srcAsset/Navigation';
import SplashScreen from "react-native-splash-screen";
import RNImage from "./src/component/RNImage";
import RNButton from "./src/component/RNButton";
import { color } from "./src/utils/color";


function App() {
  const [isConnectedState, setIsConnectedState] = useState<boolean | null>(
    true
  );
  LogBox.ignoreAllLogs();
  useEffect(() => {
    SplashScreen.hide();
    // Keep the screen awake for scanner functionality
    KeepAwake.activate();

    // Cleanup function to deactivate when component unmounts
    return () => {
      KeepAwake.deactivate();
    };
  }, []);

  useEffect(() => {
    const unsubscribe = addEventListener((state) => {
        setIsConnectedState(state.isConnected && state.isInternetReachable );
    });
    return () => {
        unsubscribe();
    };
}, []);

const [isLoading, setIsLoading] = useState(false);

const handelRefrech = () => {
    setIsLoading(true);
    setTimeout(() => {
      unsubscribe();
      setIsLoading(false);
    }, 1000);
    const unsubscribe = addEventListener((state) => {
        setIsConnectedState(state.isConnected);
      });
};

  

  return (
    <SafeAreaView style={styles.mainView}>
       {isConnectedState ? (
                    <Navigation />
                ) : (
                    <>
                        <View
                            style={{
                                flex: 1,
                                backgroundColor: color.BACKGROUND_COLOR,
                                alignItems: 'center',
                                justifyContent: 'center',
                            }}
                        >
                            <RNImage source={require("./src/assets/Images/NoInternet.png")}
                            style={{
                                backgroundColor: color.BACKGROUND_COLOR,
                                alignItems: 'center',
                                justifyContent: 'center',width: 110, height: 110 ,}}
                            resizeMode="cover"
                            />
                            {isLoading ? (
                                <Spinner animating={isLoading} />
                            ) : (
                                <>
                                    <Text
                                        style={{
                                            fontSize: 20,
                                            color: color.BLACK
                                        }}
                                    >
                                        No internet
                                    </Text>
                                    <RNButton style={{backgroundColor:color.DARK_BLUE,paddingVertical:10,paddingHorizontal:40,borderRadius:10,marginTop:10}}
                                        handelOnPress={() => handelRefrech()}
                                    >
                                        <Text style={{color:color.WHITE,fontSize:18}}>Refresh</Text>
                                    </RNButton>
                                </>
                            )}
                        </View>
                    </>
                )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  mainView: {
    flex: 1,
  },
});

export default App;
